<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Inter-Regular.ttf">
            <string>Inter-Regular</string>
        </array>
        <array key="Inter-SemiBold.ttf">
            <string>Inter-SemiBold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="ApplePayConfirmationTVC" rowHeight="1310" id="KGk-i7-Jjw" customClass="ApplePayConfirmationTVC" customModule="Motorgy" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="660" height="1310"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="660" height="1310"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IpE-vt-uwP">
                        <rect key="frame" x="0.0" y="0.0" width="660" height="1310"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="qhj-8A-TLP">
                                <rect key="frame" x="24" y="0.0" width="612" height="1210"/>
                                <subviews>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2rB-Fr-nQ4">
                                        <rect key="frame" x="0.0" y="0.0" width="612" height="213.33333333333334"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="acceptIcon" translatesAutoresizingMaskIntoConstraints="NO" id="UZC-gd-bpb">
                                                <rect key="frame" x="256" y="24" width="100" height="100"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="100" id="ubv-mu-Llt"/>
                                                    <constraint firstAttribute="height" constant="100" id="zEy-tA-Haw"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Pr-vo-L0C" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24" y="148" width="564" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBrownColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="5Pr-vo-L0C" secondAttribute="trailing" constant="24" id="8LE-Wi-zO3"/>
                                            <constraint firstItem="5Pr-vo-L0C" firstAttribute="top" secondItem="UZC-gd-bpb" secondAttribute="bottom" constant="24" id="NrI-ju-nFr"/>
                                            <constraint firstItem="UZC-gd-bpb" firstAttribute="centerX" secondItem="2rB-Fr-nQ4" secondAttribute="centerX" id="Ucb-qy-Bgg"/>
                                            <constraint firstItem="UZC-gd-bpb" firstAttribute="top" secondItem="2rB-Fr-nQ4" secondAttribute="top" constant="24" id="Z9X-or-mxA"/>
                                            <constraint firstAttribute="bottom" secondItem="5Pr-vo-L0C" secondAttribute="bottom" constant="24" id="jaI-x3-t6B"/>
                                            <constraint firstItem="5Pr-vo-L0C" firstAttribute="leading" secondItem="2rB-Fr-nQ4" secondAttribute="leading" constant="24" id="t0t-RS-Qbx"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="uts-xo-2kL">
                                        <rect key="frame" x="0.0" y="237.33333333333334" width="612" height="104.66666666666666"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="aboutSide" translatesAutoresizingMaskIntoConstraints="NO" id="5Xj-Gb-CUJ">
                                                <rect key="frame" x="24" y="39.999999999999972" width="24" height="24"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="24" id="02k-SC-XpQ"/>
                                                    <constraint firstAttribute="height" constant="24" id="dC1-VV-6xH"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="grG-Wi-7ah" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="60" y="24" width="528" height="20.333333333333329"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tDh-Ge-U4U" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="60" y="60.333333333333343" width="41.333333333333343" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZhO-bm-RQI" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="113.33333333333333" y="60.333333333333343" width="41.333333333333329" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemCyanColor"/>
                                        <constraints>
                                            <constraint firstItem="5Xj-Gb-CUJ" firstAttribute="leading" secondItem="uts-xo-2kL" secondAttribute="leading" constant="24" id="2VJ-Q5-Iqd"/>
                                            <constraint firstItem="grG-Wi-7ah" firstAttribute="top" secondItem="uts-xo-2kL" secondAttribute="top" constant="24" id="9CB-0v-3uT"/>
                                            <constraint firstItem="tDh-Ge-U4U" firstAttribute="leading" secondItem="grG-Wi-7ah" secondAttribute="leading" id="H43-eA-HGg"/>
                                            <constraint firstItem="tDh-Ge-U4U" firstAttribute="top" secondItem="grG-Wi-7ah" secondAttribute="bottom" constant="16" id="Ojd-He-O7F"/>
                                            <constraint firstItem="5Xj-Gb-CUJ" firstAttribute="top" secondItem="uts-xo-2kL" secondAttribute="top" constant="40" id="bLd-O3-Xhy"/>
                                            <constraint firstItem="grG-Wi-7ah" firstAttribute="leading" secondItem="5Xj-Gb-CUJ" secondAttribute="trailing" constant="12" id="l0J-Ir-peO"/>
                                            <constraint firstItem="ZhO-bm-RQI" firstAttribute="centerY" secondItem="tDh-Ge-U4U" secondAttribute="centerY" id="liF-EE-KNU"/>
                                            <constraint firstAttribute="bottom" secondItem="tDh-Ge-U4U" secondAttribute="bottom" constant="24" id="ok1-Yb-L0G"/>
                                            <constraint firstItem="ZhO-bm-RQI" firstAttribute="leading" secondItem="tDh-Ge-U4U" secondAttribute="trailing" constant="12" id="pw6-Y1-bZc"/>
                                            <constraint firstAttribute="trailing" secondItem="grG-Wi-7ah" secondAttribute="trailing" constant="24" id="uya-Cc-dmc"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="BEs-V8-a51">
                                        <rect key="frame" x="0.0" y="366" width="612" height="361"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P7u-eI-Uc9" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24" y="24" width="564" height="20.333333333333329"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oQY-cx-bky" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24.000000000000004" y="68.333333333333314" width="41.333333333333343" height="20.333333333333329"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eYa-z3-sse" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24.000000000000004" y="157" width="41.333333333333343" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rw3-Zd-nGq" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24.000000000000004" y="201.33333333333337" width="41.333333333333343" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L4K-uh-THF" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24.000000000000004" y="245.66666666666663" width="41.333333333333343" height="20.333333333333314"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UAn-IX-azl" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="546.66666666666663" y="68.333333333333314" width="41.333333333333371" height="20.333333333333329"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mMT-m6-KRg" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="546.66666666666663" y="157" width="41.333333333333371" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jC5-SZ-MJt" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="546.66666666666663" y="201.33333333333337" width="41.333333333333371" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="li9-hw-AeF" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="546.66666666666663" y="245.66666666666663" width="41.333333333333371" height="20.333333333333314"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Sud-wW-cPu" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24.000000000000004" y="112.66666666666669" width="41.333333333333343" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0Hb-9D-3mZ" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="546.66666666666663" y="112.66666666666669" width="41.333333333333371" height="20.333333333333343"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemCyanColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="UAn-IX-azl" secondAttribute="trailing" constant="24" id="0G5-dp-HwN"/>
                                            <constraint firstItem="jC5-SZ-MJt" firstAttribute="centerY" secondItem="rw3-Zd-nGq" secondAttribute="centerY" id="0o6-rg-Fs6"/>
                                            <constraint firstItem="eYa-z3-sse" firstAttribute="top" secondItem="Sud-wW-cPu" secondAttribute="bottom" constant="24" id="55s-6e-mgS"/>
                                            <constraint firstItem="L4K-uh-THF" firstAttribute="top" secondItem="rw3-Zd-nGq" secondAttribute="bottom" constant="24" id="6ha-QE-dfI"/>
                                            <constraint firstAttribute="trailing" secondItem="P7u-eI-Uc9" secondAttribute="trailing" constant="24" id="7UJ-sY-4kI"/>
                                            <constraint firstAttribute="bottom" secondItem="L4K-uh-THF" secondAttribute="bottom" constant="24" id="A48-hA-VEX"/>
                                            <constraint firstItem="rw3-Zd-nGq" firstAttribute="top" secondItem="eYa-z3-sse" secondAttribute="bottom" constant="24" id="GXO-XW-FBB"/>
                                            <constraint firstAttribute="trailing" secondItem="jC5-SZ-MJt" secondAttribute="trailing" constant="24" id="MNf-Mm-NAo"/>
                                            <constraint firstItem="Sud-wW-cPu" firstAttribute="top" secondItem="oQY-cx-bky" secondAttribute="bottom" constant="24" id="O87-zS-xC8"/>
                                            <constraint firstItem="rw3-Zd-nGq" firstAttribute="leading" secondItem="BEs-V8-a51" secondAttribute="leading" constant="24" id="Qvt-Gu-Orc"/>
                                            <constraint firstItem="P7u-eI-Uc9" firstAttribute="leading" secondItem="BEs-V8-a51" secondAttribute="leading" constant="24" id="QyJ-cI-w3R"/>
                                            <constraint firstItem="li9-hw-AeF" firstAttribute="centerY" secondItem="L4K-uh-THF" secondAttribute="centerY" id="UAe-Z6-5DG"/>
                                            <constraint firstItem="oQY-cx-bky" firstAttribute="top" secondItem="P7u-eI-Uc9" secondAttribute="bottom" constant="24" id="YFH-ih-wJo"/>
                                            <constraint firstItem="P7u-eI-Uc9" firstAttribute="top" secondItem="BEs-V8-a51" secondAttribute="top" constant="24" id="ZxU-f5-0re"/>
                                            <constraint firstItem="0Hb-9D-3mZ" firstAttribute="centerY" secondItem="Sud-wW-cPu" secondAttribute="centerY" id="bjB-zh-ltC"/>
                                            <constraint firstItem="Sud-wW-cPu" firstAttribute="leading" secondItem="BEs-V8-a51" secondAttribute="leading" constant="24" id="es1-bp-P3s"/>
                                            <constraint firstItem="oQY-cx-bky" firstAttribute="leading" secondItem="BEs-V8-a51" secondAttribute="leading" constant="24" id="j2J-57-f2r"/>
                                            <constraint firstAttribute="trailing" secondItem="li9-hw-AeF" secondAttribute="trailing" constant="24" id="ldc-mB-fHZ"/>
                                            <constraint firstItem="mMT-m6-KRg" firstAttribute="centerY" secondItem="eYa-z3-sse" secondAttribute="centerY" id="mED-LV-C0y"/>
                                            <constraint firstAttribute="trailing" secondItem="mMT-m6-KRg" secondAttribute="trailing" constant="24" id="oUm-vQ-0jI"/>
                                            <constraint firstAttribute="trailing" secondItem="0Hb-9D-3mZ" secondAttribute="trailing" constant="24" id="okB-DJ-JfO"/>
                                            <constraint firstItem="L4K-uh-THF" firstAttribute="leading" secondItem="BEs-V8-a51" secondAttribute="leading" constant="24" id="rVv-qn-LoW"/>
                                            <constraint firstItem="UAn-IX-azl" firstAttribute="centerY" secondItem="oQY-cx-bky" secondAttribute="centerY" id="ty3-nf-SxI"/>
                                            <constraint firstItem="eYa-z3-sse" firstAttribute="leading" secondItem="BEs-V8-a51" secondAttribute="leading" constant="24" id="wzV-BQ-rjR"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" ambiguous="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bH7-OA-CQF">
                                        <rect key="frame" x="0.0" y="751" width="612" height="459"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" ambiguous="YES" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="98M-wW-juX" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                <rect key="frame" x="24" y="24" width="564" height="261"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <stackView opaque="NO" contentMode="scaleToFill" ambiguous="YES" axis="vertical" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Kzy-kg-jgB">
                                                <rect key="frame" x="24" y="301" width="564" height="199"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="j0I-IT-8ux">
                                                        <rect key="frame" x="0.0" y="0.0" width="564" height="17"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GLT-3t-P8e" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="511" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-Regular" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="slate"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-Regular"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ieq-9w-JVP" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="527" y="0.0" width="37" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-SemiBold" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="charcoal"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-SemiBold"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="Ieq-9w-JVP" firstAttribute="leading" secondItem="GLT-3t-P8e" secondAttribute="trailing" constant="16" id="QrK-26-Pge"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="xAX-E7-vKA">
                                                        <rect key="frame" x="0.0" y="33" width="564" height="17"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WWv-xU-JqS" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="511" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-Regular" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="slate"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-Regular"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="L6Q-PL-VFh" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="527" y="0.0" width="37" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-SemiBold" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="charcoal"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-SemiBold"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="L6Q-PL-VFh" firstAttribute="leading" secondItem="WWv-xU-JqS" secondAttribute="trailing" constant="16" id="EDC-W7-ssT"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="SvZ-Mp-Srg">
                                                        <rect key="frame" x="0.0" y="66" width="564" height="17"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UnC-ZP-GlZ" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="511" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-Regular" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="slate"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-Regular"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="73V-ba-wl2" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="527" y="0.0" width="37" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-SemiBold" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="charcoal"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-SemiBold"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="UnC-ZP-GlZ" firstAttribute="leading" secondItem="SvZ-Mp-Srg" secondAttribute="leading" id="7J7-hw-VoA"/>
                                                            <constraint firstItem="UnC-ZP-GlZ" firstAttribute="top" secondItem="SvZ-Mp-Srg" secondAttribute="top" id="8rx-ix-sFX"/>
                                                            <constraint firstAttribute="bottom" secondItem="73V-ba-wl2" secondAttribute="bottom" id="Wmx-aF-RTa"/>
                                                            <constraint firstItem="73V-ba-wl2" firstAttribute="top" secondItem="SvZ-Mp-Srg" secondAttribute="top" id="eD2-Z8-rr9"/>
                                                            <constraint firstAttribute="bottom" secondItem="UnC-ZP-GlZ" secondAttribute="bottom" id="ft1-wT-fhr"/>
                                                            <constraint firstAttribute="trailing" secondItem="73V-ba-wl2" secondAttribute="trailing" id="oYk-e6-MRi"/>
                                                            <constraint firstItem="73V-ba-wl2" firstAttribute="leading" secondItem="UnC-ZP-GlZ" secondAttribute="trailing" constant="16" id="x5E-z8-7Y9"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="dZw-XP-Xxa">
                                                        <rect key="frame" x="0.0" y="99" width="564" height="17"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Lla-k7-foR" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="511" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-Regular" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="slate"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-Regular"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xeV-Ug-H7i" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="527" y="0.0" width="37" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-SemiBold" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="charcoal"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-SemiBold"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="xeV-Ug-H7i" firstAttribute="leading" secondItem="Lla-k7-foR" secondAttribute="trailing" constant="16" id="99q-oD-shA"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="s2b-bm-JuI">
                                                        <rect key="frame" x="0.0" y="132" width="564" height="17"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P1w-Yw-3sp" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="511" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-Regular" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="slate"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-Regular"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mvw-Mt-JVj" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="527" y="0.0" width="37" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-SemiBold" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="charcoal"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-SemiBold"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="mvw-Mt-JVj" firstAttribute="leading" secondItem="P1w-Yw-3sp" secondAttribute="trailing" constant="16" id="l3X-nw-amQ"/>
                                                        </constraints>
                                                    </stackView>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dottedLineee" translatesAutoresizingMaskIntoConstraints="NO" id="dR0-C7-h9J">
                                                        <rect key="frame" x="0.0" y="165" width="564" height="1"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="x9U-1a-euG"/>
                                                        </constraints>
                                                    </imageView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="5wz-CK-N3P">
                                                        <rect key="frame" x="0.0" y="182" width="564" height="17"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1rH-db-BXL" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="0.0" y="0.0" width="511" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-Regular" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="slate"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-Regular"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rLx-xv-jgS" customClass="labelLocalization" customModule="Motorgy" customModuleProvider="target">
                                                                <rect key="frame" x="527" y="0.0" width="37" height="17"/>
                                                                <fontDescription key="fontDescription" name="Inter-SemiBold" family="Inter" pointSize="14"/>
                                                                <color key="textColor" name="charcoal"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="fontSizeAR">
                                                                        <real key="value" value="14"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="fontNameAR" value="Cairo-SemiBold"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="rLx-xv-jgS" firstAttribute="leading" secondItem="1rH-db-BXL" secondAttribute="trailing" constant="16" id="1V7-pj-baF"/>
                                                        </constraints>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemCyanColor"/>
                                        <constraints>
                                            <constraint firstItem="Kzy-kg-jgB" firstAttribute="leading" secondItem="bH7-OA-CQF" secondAttribute="leading" constant="24" id="96T-Pl-Oa1"/>
                                            <constraint firstItem="98M-wW-juX" firstAttribute="top" secondItem="bH7-OA-CQF" secondAttribute="top" constant="24" id="Ld4-ev-qTI"/>
                                            <constraint firstAttribute="trailing" secondItem="Kzy-kg-jgB" secondAttribute="trailing" constant="24" id="NvJ-lD-7IO"/>
                                            <constraint firstAttribute="bottom" secondItem="Kzy-kg-jgB" secondAttribute="bottom" constant="16" id="PVX-9v-za8"/>
                                            <constraint firstItem="Kzy-kg-jgB" firstAttribute="top" secondItem="98M-wW-juX" secondAttribute="bottom" constant="16" id="eDm-IH-l9X"/>
                                            <constraint firstAttribute="trailing" secondItem="98M-wW-juX" secondAttribute="trailing" constant="24" id="jVh-Jb-lcd"/>
                                            <constraint firstItem="98M-wW-juX" firstAttribute="leading" secondItem="bH7-OA-CQF" secondAttribute="leading" constant="24" id="npI-UN-0DS"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="uts-xo-2kL" secondAttribute="trailing" id="6SM-gc-nvf"/>
                                    <constraint firstItem="uts-xo-2kL" firstAttribute="leading" secondItem="qhj-8A-TLP" secondAttribute="leading" id="pvo-cM-WCG"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="qhj-8A-TLP" firstAttribute="leading" secondItem="IpE-vt-uwP" secondAttribute="leading" constant="24" id="BVa-pQ-kXG"/>
                            <constraint firstAttribute="bottom" secondItem="qhj-8A-TLP" secondAttribute="bottom" constant="100" id="Ezo-50-mcI"/>
                            <constraint firstItem="qhj-8A-TLP" firstAttribute="top" secondItem="IpE-vt-uwP" secondAttribute="top" id="j8j-46-MT7"/>
                            <constraint firstAttribute="trailing" secondItem="qhj-8A-TLP" secondAttribute="trailing" constant="24" id="xTI-y9-2i9"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="IpE-vt-uwP" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="AJC-WH-r36"/>
                    <constraint firstItem="IpE-vt-uwP" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Cqb-8f-lY6"/>
                    <constraint firstAttribute="bottom" secondItem="IpE-vt-uwP" secondAttribute="bottom" id="ICr-sZ-dhZ"/>
                    <constraint firstAttribute="trailing" secondItem="IpE-vt-uwP" secondAttribute="trailing" id="mQ7-J5-8S2"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="billSummaryLabel" destination="98M-wW-juX" id="qmJ-Sp-U2U"/>
                <outlet property="carName" destination="grG-Wi-7ah" id="4pR-HE-GHZ"/>
                <outlet property="dateLabel" destination="L4K-uh-THF" id="C2j-TV-uo2"/>
                <outlet property="dateValueLabel" destination="li9-hw-AeF" id="bbg-Dg-OMH"/>
                <outlet property="discountAmount" destination="xeV-Ug-H7i" id="6h9-h1-Guu"/>
                <outlet property="discountLabel" destination="Lla-k7-foR" id="YaN-z2-OWS"/>
                <outlet property="discountStackView" destination="dZw-XP-Xxa" id="zcn-ro-X87"/>
                <outlet property="dottedLine" destination="dR0-C7-h9J" id="HtW-Gg-giJ"/>
                <outlet property="fourthView" destination="bH7-OA-CQF" id="9HW-ee-Stg"/>
                <outlet property="idLabel" destination="UAn-IX-azl" id="sME-df-yWX"/>
                <outlet property="microDealerBoostNameLabel" destination="WWv-xU-JqS" id="sM5-a2-5Mw"/>
                <outlet property="microDealerBoostStackView" destination="xAX-E7-vKA" id="S7G-Lv-L7S"/>
                <outlet property="microDealerBoostValueLabel" destination="L6Q-PL-VFh" id="S0f-7Z-yfV"/>
                <outlet property="mileageLabel" destination="ZhO-bm-RQI" id="61Q-Zp-7px"/>
                <outlet property="packageLabel" destination="GLT-3t-P8e" id="XGa-1S-71e"/>
                <outlet property="packagePrice" destination="Ieq-9w-JVP" id="ARD-mL-VtV"/>
                <outlet property="packageStackView" destination="j0I-IT-8ux" id="g8O-MK-sOh"/>
                <outlet property="paymentDetailsLabel" destination="P7u-eI-Uc9" id="PNV-w7-VYe"/>
                <outlet property="paymentIdLabel" destination="oQY-cx-bky" id="iMt-Lb-tQf"/>
                <outlet property="paymentTypeLabel" destination="eYa-z3-sse" id="GVR-ia-POI"/>
                <outlet property="resultLabel" destination="rw3-Zd-nGq" id="Gp8-tu-0kq"/>
                <outlet property="resultValueLabel" destination="jC5-SZ-MJt" id="Ock-Uu-6mB"/>
                <outlet property="secondView" destination="uts-xo-2kL" id="MgN-YU-Rvm"/>
                <outlet property="subTotalLabel" destination="UnC-ZP-GlZ" id="BsN-l8-kGU"/>
                <outlet property="subTotalStackView" destination="SvZ-Mp-Srg" id="CiH-RK-2VP"/>
                <outlet property="subtotalAmount" destination="73V-ba-wl2" id="Baa-Jb-BTQ"/>
                <outlet property="successFailureIcon" destination="UZC-gd-bpb" id="21x-7x-7JB"/>
                <outlet property="thirdView" destination="BEs-V8-a51" id="CE1-Vq-Qq5"/>
                <outlet property="topHeaderView" destination="2rB-Fr-nQ4" id="EPv-lT-763"/>
                <outlet property="topTitle" destination="5Pr-vo-L0C" id="Akw-JH-Hap"/>
                <outlet property="totalAmount" destination="rLx-xv-jgS" id="s9L-G9-eF7"/>
                <outlet property="totalLabel" destination="1rH-db-BXL" id="ZZ4-qv-uFk"/>
                <outlet property="totalStackView" destination="5wz-CK-N3P" id="OKw-0e-b3H"/>
                <outlet property="transactionLabel" destination="Sud-wW-cPu" id="gnT-iP-dEW"/>
                <outlet property="transactionValueLabel" destination="0Hb-9D-3mZ" id="iLW-aS-q6x"/>
                <outlet property="typeLabel" destination="mMT-m6-KRg" id="dJL-PV-ftX"/>
                <outlet property="walletLabel" destination="P1w-Yw-3sp" id="REa-bl-yZD"/>
                <outlet property="walletStackView" destination="s2b-bm-JuI" id="Qe6-x2-UUN"/>
                <outlet property="walletValue" destination="mvw-Mt-JVj" id="dpe-tq-xFS"/>
                <outlet property="yearLabel" destination="tDh-Ge-U4U" id="hcr-EV-oUd"/>
            </connections>
            <point key="canvasLocation" x="352.67175572519085" y="240.14084507042256"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="aboutSide" width="18" height="18"/>
        <image name="acceptIcon" width="16" height="16.333333969116211"/>
        <image name="dottedLineee" width="327" height="1"/>
        <namedColor name="charcoal">
            <color red="0.054999999701976776" green="0.059000000357627869" blue="0.059000000357627869" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="slate">
            <color red="0.28627450980392155" green="0.34509803921568627" blue="0.40392156862745099" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBrownColor">
            <color red="0.63529411759999999" green="0.51764705879999995" blue="0.36862745099999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemCyanColor">
            <color red="0.1960784314" green="0.67843137249999996" blue="0.90196078430000004" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
