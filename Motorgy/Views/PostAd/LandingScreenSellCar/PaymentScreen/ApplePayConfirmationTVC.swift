//
//  ApplePayConfirmationTVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 02/09/2024.
//  Copyright © 2024 <PERSON><PERSON>. All rights reserved.
//

import UIKit

class ApplePayConfirmationTVC: UITableViewCell, CellReusableIdentifier {
    @IBOutlet weak var topHeaderView: UIView!
    @IBOutlet weak var secondView: UIView!
    @IBOutlet weak var thirdView: UIView!
    @IBOutlet weak var fourthView: UIView!
    @IBOutlet weak var successFailureIcon: UIImageView!
    @IBOutlet weak var topTitle: labelLocalization!
    @IBOutlet weak var carName: labelLocalization!
    @IBOutlet weak var yearLabel: labelLocalization!
    @IBOutlet weak var mileageLabel: labelLocalization!
    @IBOutlet weak var paymentDetailsLabel: labelLocalization!
    @IBOutlet weak var paymentIdLabel: labelLocalization!
    @IBOutlet weak var paymentTypeLabel: labelLocalization!
    @IBOutlet weak var resultLabel: labelLocalization!
    @IBOutlet weak var dateLabel: labelLocalization!
    @IBOutlet weak var idLabel: labelLocalization!
    @IBOutlet weak var typeLabel: labelLocalization!
    @IBOutlet weak var resultValueLabel: labelLocalization!
    @IBOutlet weak var dateValueLabel: labelLocalization!
    @IBOutlet weak var billSummaryLabel: labelLocalization!
    @IBOutlet weak var transactionLabel: labelLocalization!
    @IBOutlet weak var transactionValueLabel: labelLocalization!
    @IBOutlet weak var packageStackView: UIStackView!
    @IBOutlet weak var packageLabel: labelLocalization!
    @IBOutlet weak var packagePrice: labelLocalization!
    @IBOutlet weak var subTotalStackView: UIStackView!
    @IBOutlet weak var subTotalLabel: labelLocalization!
    @IBOutlet weak var subtotalAmount: labelLocalization!
    @IBOutlet weak var discountStackView: UIStackView!
    @IBOutlet weak var discountLabel: labelLocalization!
    @IBOutlet weak var discountAmount: labelLocalization!
    @IBOutlet weak var totalStackView: UIStackView!
    @IBOutlet weak var totalLabel: labelLocalization!
    @IBOutlet weak var totalAmount: labelLocalization!
    @IBOutlet weak var dottedLine: UIImageView!
    @IBOutlet weak var walletLabel: labelLocalization!
    @IBOutlet weak var walletValue: labelLocalization!
	@IBOutlet weak var walletStackView: UIStackView!
    @IBOutlet weak var microDealerBoostStackView: UIStackView!
    @IBOutlet weak var microDealerBoostValueLabel: labelLocalization!
    @IBOutlet weak var microDealerBoostNameLabel: labelLocalization!
	
    func configureCell(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any], selectedPackage: LstPackages?, userData: [String: Any]?, isTrim: Bool, promoCodeDiscountAmount: Double) {
        [self.secondView, self.thirdView, self.fourthView].forEach {
            $0?.backgroundColor = .white
            $0?.layer.borderColor = UIColor.gray.withAlphaComponent(0.8).cgColor
            $0?.layer.borderWidth = 0.75
            $0?.layer.cornerRadius = 8
            
            $0?.layer.shadowOpacity = 0.22
            $0?.layer.shadowRadius = 8
            $0?.layer.shadowOffset = CGSize(width: 0, height: 3)
            $0?.layer.shadowColor = UIColor(red: 0.07, green: 0.07, blue: 0.07, alpha: 1).cgColor
            $0?.clipsToBounds = false
        }
        
        if screenStatus == .success {
            self.successFailureIcon.image = UIImage(named: "apply-finance")
            self.topTitle.text = "Payment successful!".localized
            self.topHeaderView.backgroundColor = .green.withAlphaComponent(0.1)
        } else {
            self.successFailureIcon.image = UIImage(named: "apply-finace-not")
            self.topTitle.text = "Payment failed!".localized
            self.topHeaderView.backgroundColor = .red.withAlphaComponent(0.1)
        }
        
        self.topTitle.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: LanguageHelper.isEnglish ? 16 : 14)
        self.topTitle.textColor = Colors.charcoalColor
        self.topTitle.textAlignment = .center
        
        self.carDetailsSection(isTrim: isTrim, userData: userData)
        
        self.paymentDetailsSection(userData: userData, dic: dic)
        
        self.billSummaryLabel.text = "Bill summary".localized
        self.billSummaryLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 15 : 13)
        self.billSummaryLabel.textColor = Colors.charcoalColor
        
        self.packageStackView.isHidden = false
        
        let packageServiceType = selectedPackage?.isSelfService ?? false ? (LanguageHelper.isEnglish ? "Self service" : "الخدمة الذاتية") : "Concierge".localized
        self.packageLabel.text = "\(packageServiceType) - \(selectedPackage?.packageName ?? "")"
        self.packageLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.packageLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        self.packageLabel.numberOfLines = 2
        
        let packagePrice = selectedPackage?.discountPrice ?? 0.0 > 0 ? selectedPackage?.discountPrice ?? 0.0 : selectedPackage?.price ?? 0.0
        self.packagePrice.text = "\(packagePrice.formattedWithWithoutFraction()) " + "KWD".localized
        self.packagePrice.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.packagePrice.textColor = Colors.charcoalColor
        
        self.dottedLine.isHidden = false
        
        self.subTotalStackView.isHidden = true
        self.subTotalLabel.text = "Subtotal".localized
        self.subTotalLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.subtotalAmount.text = packagePrice == 0.0 ? "" : "\(packagePrice.formattedWithWithoutFraction()) " + "KWD".localized
        self.subtotalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        
        self.discountStackView.isHidden = true
        self.discountLabel.text = "Discount".localized
        self.discountLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        let discountAmount = (selectedPackage?.price ?? 0.0) - (selectedPackage?.discountPrice ?? 0.0)
        self.discountAmount.text =  discountAmount == 0.0 ? "" : "-\(discountAmount.formattedWithWithoutFraction()) " + "KWD".localized
        self.discountAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.discountAmount.textColor = Colors.grapefruitColor
        
        self.totalStackView.isHidden = false
        self.totalLabel.text = "Total".localized
        self.totalLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        
        self.totalAmount.text = packagePrice == 0.0 ? "" : "\(packagePrice.formattedWithWithoutFraction()) " + "KWD".localized
        self.totalAmount.textColor = Colors.bluishColor
        
        if promoCodeDiscountAmount > 0 {
            self.subTotalStackView.isHidden = false
            let subTotalAmountBeforePromoCode = packagePrice
            self.subtotalAmount.text = subTotalAmountBeforePromoCode == 0.0 ? "" : "\(subTotalAmountBeforePromoCode.formattedWithWithoutFraction()) " + "KWD".localized
            
            self.discountStackView.isHidden = false
            self.discountAmount.text = promoCodeDiscountAmount == 0.0 ? "" : "-\(promoCodeDiscountAmount.formattedWithWithoutFraction()) " + "KWD".localized
            self.discountAmount.textColor = Colors.grapefruitColor
            
            self.totalStackView.isHidden = false
            let totalAmount = (packagePrice - promoCodeDiscountAmount)
            self.totalAmount.text = totalAmount == 0.0 ? "" : "\(totalAmount.formattedWithWithoutFraction()) " + "KWD".localized
            self.totalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
            self.totalAmount.textColor = Colors.bluishColor
        }
        
        configureWalletUsedStackView()
    }
    
    private func configureWalletUsedStackView() {
        if ConstantsValues.sharedInstance.amountOfWalletBalanceUsed == "" {
            walletStackView.isHidden = true
        } else {
            walletStackView.isHidden = false
            walletLabel.text = LanguageHelper.isEnglish ? "Balance used" : "الرصيد المستخدم"
            walletLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
            walletLabel.textColor = Colors.charcoalColor
            
            walletValue.text = ConstantsValues.sharedInstance.amountOfWalletBalanceUsed
            walletValue.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
            walletValue.textColor = Colors.charcoalColor
        }
    }
    
    func configureCellForMarketPlace(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any], userData: [String: Any]?, isTrim: Bool, promoCodeDiscountAmount: Double, isOpenFromBoost: Bool, boostService: BoostServices?) {
        [self.thirdView, self.fourthView].forEach {
            $0?.backgroundColor = .white
            $0?.layer.borderColor = UIColor.gray.withAlphaComponent(0.8).cgColor
            $0?.layer.borderWidth = 0.75
            $0?.layer.cornerRadius = 8
            
            $0?.layer.shadowOpacity = 0.22
            $0?.layer.shadowRadius = 8
            $0?.layer.shadowOffset = CGSize(width: 0, height: 3)
            $0?.layer.shadowColor = UIColor(red: 0.07, green: 0.07, blue: 0.07, alpha: 1).cgColor
            $0?.clipsToBounds = false
        }
        
        if screenStatus == .success {
            self.successFailureIcon.image = UIImage(named: "apply-finance")
            self.topTitle.text = "Payment successful!".localized
            self.topHeaderView.backgroundColor = .green.withAlphaComponent(0.1)
        } else {
            self.successFailureIcon.image = UIImage(named: "apply-finace-not")
            self.topTitle.text = "Payment failed!".localized
            self.topHeaderView.backgroundColor = .red.withAlphaComponent(0.1)
        }
        
        self.topTitle.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: LanguageHelper.isEnglish ? 16 : 14)
        self.topTitle.textColor = Colors.charcoalColor
        self.topTitle.textAlignment = .center
        
        self.secondView.isHidden = true
        
        self.paymentDetailsSection(userData: userData, dic: dic)
        
        self.billSummaryLabel.text = "Bill summary".localized
        self.billSummaryLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 15 : 13)
        self.billSummaryLabel.textColor = Colors.charcoalColor
        
        self.packageStackView.isHidden = false
        
        let inspectionPacakgeName = MarketPlaceDataSource.shared.getSelectedServices()?.name
        let isInspectionFlag = MarketPlaceDataSource.shared.getSelectedStepTypeForEachMarketPlaceFlow() == .inspection
        
        self.packageLabel.text = isOpenFromBoost ? boostService?.Title ?? "" : isInspectionFlag ? inspectionPacakgeName : MarketPlaceDataSource.shared.getSelectedPackage()?.packageName ?? ""
        self.packageLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.packageLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        self.packageLabel.numberOfLines = 2
        
        let packagePrice = MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0 > 0 ? MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0 : MarketPlaceDataSource.shared.getSelectedPackage()?.price ?? 0.0
        
        let inspectionPrice = MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0.0 > 0 ? MarketPlaceDataSource.shared.getSelectedServices()?.startsFromDiscountedPrice ?? 0.0 : MarketPlaceDataSource.shared.getSelectedServices()?.startsFromPrice ?? 0.0
        
        self.packagePrice.text = isOpenFromBoost
        	? "\(boostService?.Price?.formattedWithWithoutFraction() ?? "") " + "KWD".localized
        	: isInspectionFlag ? "\(inspectionPrice.formattedWithWithoutFraction()) " + "KWD".localized : "\(packagePrice.formattedWithWithoutFraction()) " + "KWD".localized
        self.packagePrice.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.packagePrice.textColor = Colors.charcoalColor
        
        self.dottedLine.isHidden = false
        
        self.subTotalStackView.isHidden = true
        self.subTotalLabel.text = "Subtotal".localized
        self.subTotalLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.subtotalAmount.text = packagePrice == 0.0 ? "" : "\(packagePrice.formattedWithWithoutFraction()) " + "KWD".localized
        self.subtotalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        
        self.discountStackView.isHidden = true
        self.discountLabel.text = "Discount".localized
        self.discountLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        let discountAmount = (MarketPlaceDataSource.shared.getSelectedPackage()?.price ?? 0.0) - (MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0)
        self.discountAmount.text =  discountAmount == 0.0 ? "" : "-\(discountAmount.formattedWithWithoutFraction()) " + "KWD".localized
        self.discountAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.discountAmount.textColor = Colors.grapefruitColor
        
        self.totalStackView.isHidden = false
        self.totalLabel.text = "Total".localized
        self.totalLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        
        self.totalAmount.text = Double((dic["amt"] as? String) ?? "") == 0.0 ? "0 \("KWD".localized)" : "\((Double((dic["amt"] as? String) ?? "") ?? 0).formattedWithWithoutFraction()) " + "KWD".localized
        self.totalAmount.textColor = Colors.bluishColor
        
        if promoCodeDiscountAmount > 0 {
            self.subTotalStackView.isHidden = false
            let subTotalAmountBeforePromoCode = packagePrice
            self.subtotalAmount.text = subTotalAmountBeforePromoCode == 0.0 ? "" : "\(subTotalAmountBeforePromoCode.formattedWithWithoutFraction()) " + "KWD".localized
            
            self.discountStackView.isHidden = false
            self.discountAmount.text = promoCodeDiscountAmount == 0.0 ? "" : "-\(promoCodeDiscountAmount.formattedWithWithoutFraction()) " + "KWD".localized
            self.discountAmount.textColor = Colors.grapefruitColor
            
            self.totalStackView.isHidden = false
            let totalAmount = (packagePrice - promoCodeDiscountAmount)
            self.totalAmount.text = totalAmount == 0.0 ? "" : "\(totalAmount.formattedWithWithoutFraction()) " + "KWD".localized
            self.totalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
            self.totalAmount.textColor = Colors.bluishColor
        }
        
        configureWalletUsedStackView()
    }
    
	private func toggleShowMicroDealerBoost(hide: Bool) {
		self.microDealerBoostStackView.isHidden = hide
		self.microDealerBoostNameLabel.isHidden = hide
		self.microDealerBoostValueLabel.isHidden = hide
	}
	
    func configureCellForMicroDealerBundleFlow(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any], userData: [String: Any]?, isTrim: Bool, promoCodeDiscountAmount: Double, isOpenFromMicroDealer: Bool, microDealerViewModel: MicroDealerViewModel) {
        [self.thirdView, self.fourthView].forEach {
            $0?.backgroundColor = .white
            $0?.layer.borderColor = UIColor.gray.withAlphaComponent(0.8).cgColor
            $0?.layer.borderWidth = 0.75
            $0?.layer.cornerRadius = 8
            
            $0?.layer.shadowOpacity = 0.22
            $0?.layer.shadowRadius = 8
            $0?.layer.shadowOffset = CGSize(width: 0, height: 3)
            $0?.layer.shadowColor = UIColor(red: 0.07, green: 0.07, blue: 0.07, alpha: 1).cgColor
            $0?.clipsToBounds = false
        }
        
        if screenStatus == .success {
            self.successFailureIcon.image = UIImage(named: "apply-finance")
            self.topTitle.text = "Payment successful!".localized
            self.topHeaderView.backgroundColor = .green.withAlphaComponent(0.1)
        } else {
            self.successFailureIcon.image = UIImage(named: "apply-finace-not")
            self.topTitle.text = "Payment failed!".localized
            self.topHeaderView.backgroundColor = .red.withAlphaComponent(0.1)
        }
        
        self.topTitle.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: LanguageHelper.isEnglish ? 16 : 14)
        self.topTitle.textColor = Colors.charcoalColor
        self.topTitle.textAlignment = .center
        
        self.secondView.isHidden = true
        
        self.paymentDetailsSection(userData: userData, dic: dic)
        
        self.billSummaryLabel.text = "Bill summary".localized
        self.billSummaryLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 15 : 13)
        self.billSummaryLabel.textColor = Colors.charcoalColor
        
        self.packageStackView.isHidden = false
        
        self.packageLabel.text = microDealerViewModel.selectedPackageBundle?.packageName ?? ""
        self.packageLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.packageLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        self.packageLabel.numberOfLines = 2
        
//        let packagePrice = MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0 > 0 ? MarketPlaceDataSource.shared.getSelectedPackage()?.discountedPrice ?? 0.0 : MarketPlaceDataSource.shared.getSelectedPackage()?.price ?? 0.0
        let packagePrice = microDealerViewModel.selectedPackageBundle?.price ?? 0
		let boost = microDealerViewModel.selectedBoostPackage
		var subTotalAmount = 0.0
		var totalAmount = 0.0
        
        self.packagePrice.text = "\(packagePrice.formattedWithWithoutFraction()) " + "KWD".localized
        self.packagePrice.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.packagePrice.textColor = Colors.charcoalColor
        
        self.dottedLine.isHidden = false
		
		if let boost = microDealerViewModel.selectedBoostPackage {
			self.toggleShowMicroDealerBoost(hide: false)
			
			self.microDealerBoostNameLabel.text = boost.Title ?? ""
			self.microDealerBoostNameLabel.numberOfLines = 2
			self.microDealerBoostNameLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 13 : 12)
		} else {
			self.toggleShowMicroDealerBoost(hide: true)
		}
		
		if let boostPrice = boost?.Price {
			self.microDealerBoostValueLabel.text = "\((boostPrice).formattedWithWithoutFraction()) " + "KWD".localized
			self.microDealerBoostValueLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
		} else {
			self.microDealerBoostValueLabel.text = ""
		}
		
		self.subTotalLabel.text = "Subtotal".localized
		self.subTotalLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
		
		self.discountLabel.text = "Discount".localized
		self.discountLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
		
		self.totalLabel.text = "Total".localized
		
		if promoCodeDiscountAmount > 0 {
			self.subTotalStackView.isHidden = false
			subTotalAmount = (microDealerViewModel.selectedPackageBundle?.price ?? 0.0) + (boost?.Price ?? 0.0)
			
			self.subtotalAmount.text = subTotalAmount == 0.0 ? "" : "\(subTotalAmount.formattedWithWithoutFraction()) " + "KWD".localized
			self.subtotalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
			
			self.discountStackView.isHidden = false
			
			self.discountAmount.text = promoCodeDiscountAmount == 0.0 ? "" : "-\(promoCodeDiscountAmount.formattedWithWithoutFraction()) " + "KWD".localized
			self.discountAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
			self.discountAmount.textColor = Colors.grapefruitColor
			
			self.totalStackView.isHidden = false
			
			totalAmount = (subTotalAmount - promoCodeDiscountAmount)
			
			self.totalLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
			self.totalAmount.text = totalAmount == 0.0 ? "0 " + "KWD".localized : "\(totalAmount.formattedWithWithoutFraction()) " + "KWD".localized
			self.totalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
			self.totalAmount.textColor = Colors.bluishColor
		} else {
			self.subTotalStackView.isHidden = true
			subTotalAmount = (microDealerViewModel.selectedPackageBundle?.price ?? 0.0) + (boost?.Price ?? 0.0)
			
			self.discountStackView.isHidden = true
			
			self.totalStackView.isHidden = false
			
			totalAmount = (subTotalAmount)
			self.totalAmount.text = totalAmount == 0.0 ? "" : "\(totalAmount.formattedWithWithoutFraction()) " + "KWD".localized
			self.totalAmount.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
			self.totalAmount.textColor = Colors.bluishColor
		}
		
		self.subTotalStackView.isHidden = promoCodeDiscountAmount == 0.0
        
        configureWalletUsedStackView()
    }
    
    private func carDetailsSection(isTrim: Bool, userData: [String: Any]?) {
        let brandName = (userData?["0"] as? DataAttribute)?.name ?? ""
        let modelName = (userData?["1"] as? DataAttribute)?.name ?? ""
        let trimName = (userData?["2"] as? DataAttribute)?.name ?? ""
        
        if isTrim {
            self.carName.text = "\(brandName) \(modelName) \(trimName)"
        } else {
            self.carName.text = "\(brandName) \(modelName)"
        }
        
        self.carName.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.carName.textColor = Colors.charcoalColor
        
        self.yearLabel.text = "\((userData?[isTrim ? "3" : "2"] as? DataAttribute)?.id ?? 0)"
        self.yearLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.yearLabel.textColor = Colors.charcoalColor
        
        self.mileageLabel.text = "\(((userData?[isTrim ? "4" : "3"] as? DataAttribute)?.id ?? 0).withCommas()) " + "KM".localized
        self.mileageLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.mileageLabel.textColor = Colors.charcoalColor
    }
    
    private func paymentDetailsSection(userData: [String: Any]?, dic: [String: Any]) {
        self.paymentIdLabel.text = "Payment ID".localized
        self.paymentIdLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.paymentIdLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        
        self.idLabel.text = dic["payid"] as? String ?? "" == "" ? "-" : dic["payid"] as? String ?? ""
        self.idLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.idLabel.textColor = Colors.charcoalColor
        
        self.paymentTypeLabel.text = "Track ID".localized
        self.paymentTypeLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.paymentTypeLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        
        self.transactionLabel.text = "Transaction ID".localized
        self.transactionLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.transactionLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        
        self.typeLabel.text = dic["trackid"] as? String ?? "" == "" ? "-" : dic["trackid"] as? String ?? ""
        self.typeLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.typeLabel.textColor = Colors.charcoalColor
        
        self.transactionValueLabel.text = dic["tranid"] as? String ?? "" == "" ? "-" : dic["tranid"] as? String ?? ""
        self.transactionValueLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.transactionValueLabel.textColor = Colors.charcoalColor
        
        self.resultLabel.text = "Result".localized
        self.resultLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.resultLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        
        if dic["result"] as? String ?? "" == "CAPTURED" {
            self.resultValueLabel.text = "CAPTURED"
            ConstantsValues.sharedInstance.coordinatesFromMap = nil
        } else if dic["result"] as? String ?? "" == "NOT CAPTURED" {
            self.resultValueLabel.text = "NOT CAPTURED"
        } else {
            self.resultValueLabel.text = "Payment Failed"
        }
        
        self.resultValueLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.resultValueLabel.textColor = Colors.charcoalColor
        
        self.dateLabel.text = "Date".localized
        self.dateLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        self.dateLabel.textColor = Colors.charcoalColor.withAlphaComponent(0.8)
        
        let dateString = dic["postdate"] as? String ?? ""
        let month = String(dateString.prefix(2))
        let day = String(dateString.suffix(2))
        let currentYear = Calendar.current.component(.year, from: Date())
        let fullDateString = "\(currentYear)-\(month)-\(day)"
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm"
        
        if let date = dateFormatter.date(from: fullDateString) {
            let formattedDateString = dateFormatter.string(from: date)
            self.dateValueLabel.text = formattedDateString
        } else {
            let currentDate = Date()
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd HH:mm"
            let dateString = dateFormatter.string(from: currentDate)
            self.dateValueLabel.text = dateString
        }
        
        self.dateValueLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.dateValueLabel.textColor = Colors.charcoalColor
        
        self.paymentDetailsLabel.text = "Payment details".localized
        self.paymentDetailsLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 12)
        self.paymentDetailsLabel.textColor = Colors.charcoalColor
    }
}
