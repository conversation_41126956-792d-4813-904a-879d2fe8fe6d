//
//  TagCollectionViewCell.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 25/07/2024.
//  Copyright © 2024 Bo<PERSON>z. All rights reserved.
//

import UIKit

class TagCollectionViewCell: UICollectionViewCell {
    private let tagLabel = labelLocalization()
    private var fromFeatures = false
    private var isSelectedFlag = false
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupView() {
        tagLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(tagLabel)

        // Use fixed constraints for features (fromFeatures will be set in configure)
        tagLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16).isActive = true
        tagLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16).isActive = true
        tagLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12).isActive = true
        tagLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -12).isActive = true
        tagLabel.textAlignment = .center
        tagLabel.numberOfLines = 1
        tagLabel.lineBreakMode = .byTruncatingTail
        tagLabel.adjustsFontSizeToFitWidth = false

        // Font will be set in configure method based on selection state
    }
    
    func configure(with text: String, isSelected: Bool, itemId: Int?, fromFeatures: Bool) {
        tagLabel.text = text
        self.fromFeatures = fromFeatures
        self.isSelectedFlag = isSelected

        // Update font based on selection state and language
        tagLabel.font = UIFont(
            name: LanguageHelper.isEnglish
                ? isSelected ? "Inter-SemiBold" : "Inter-Regular"
                : isSelected ? "Cairo-SemiBold" : "Cairo-Regular",
            size: LanguageHelper.isEnglish ? 12 : 10
        )

        // Update corner radius based on fromFeatures flag
        contentView.layer.cornerRadius = fromFeatures ? 22 : 16

        // Force layout update to ensure proper sizing
        tagLabel.sizeToFit()
        setNeedsLayout()
        layoutIfNeeded()

        if itemId == -1 {
            tagLabel.textColor = Colors.slateColor
            contentView.backgroundColor = UIColor.hexStringToUIColor(hex: "#F2F4F7")
            contentView.layer.borderColor = UIColor.clear.cgColor
            contentView.layer.borderWidth = 0
        } else {
            if fromFeatures {
                tagLabel.textColor = isSelectedFlag ? Colors.bluishColor : Colors.slateColor
                contentView.backgroundColor = isSelectedFlag ? Colors.bluishColor.withAlphaComponent(0.1) : .white
                contentView.layer.borderColor = isSelectedFlag ? Colors.bluishColor.cgColor : UIColor.hexStringToUIColor(hex: "#EAECF0").cgColor
                contentView.layer.borderWidth = 1
            } else {
                tagLabel.textColor = isSelectedFlag ? UIColor.white : Colors.slateColor
                contentView.backgroundColor = isSelectedFlag ? Colors.bluishColor : UIColor.hexStringToUIColor(hex: "#F2F4F7")
                contentView.layer.borderColor = UIColor.clear.cgColor
                contentView.layer.borderWidth = 0
            }
        }
    }
}
