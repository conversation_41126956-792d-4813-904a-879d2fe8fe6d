//
//  SelfServiceFeaturesTVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 26/03/2024.
//  Copyright © 2024 <PERSON><PERSON>. All rights reserved.
//

import UIKit

protocol SelfServiceEnhancedFeaturesTVCProtocol: AnyObject {
    func didSelect(item: Int, indexPath: IndexPath, lstExtraOptionss: LstExtraOptionss?)
    func expandCollapse(indexPath: IndexPath?)
}

class SelfServiceEnhancedFeaturesTVC: UITableViewCell {
    let containerView = UIView()
    let featureTitle = UILabel()
    let collectionView: UICollectionView
    let leftIcon = UIImageView()
    let arrowImage = UIImageView()
    let dottedLineImage = UIImageView()
    let headerView = UIView()
    
    var lstExtraOptionss: LstExtraOptionss?
    private var extraOptions: [ExtraOptions]?
    private var values = [String]()
    private var ids = [Int]()
    private var itemSelectedID: Int?
    weak var delegate: SelfServiceEnhancedFeaturesTVCProtocol?
    private var indexPath: IndexPath?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        let layout = LeftAlignedCollectionViewFlowLayout()
        // Remove estimatedItemSize to avoid conflicts with sizeForItemAt
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.scrollDirection = .vertical
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)

        super.init(style: style, reuseIdentifier: reuseIdentifier)

        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)
        
        containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24).isActive = true
        containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8).isActive = true
        containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24).isActive = true
        containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8).isActive = true
        containerView.layer.backgroundColor = UIColor.white.cgColor
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.hexStringToUIColor(hex: "#EAECF0").cgColor
        
        headerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(headerView)
        
        headerView.topAnchor.constraint(equalTo: containerView.topAnchor).isActive = true
        headerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor).isActive = true
        headerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor).isActive = true
        headerView.heightAnchor.constraint(equalToConstant: 56).isActive = true
        headerView.layer.cornerRadius = 8
        headerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner] // Only top corners
        
        [featureTitle, leftIcon, arrowImage, dottedLineImage].forEach {
            $0.translatesAutoresizingMaskIntoConstraints = false
            headerView.addSubview($0)
        }
        
        leftIcon.topAnchor.constraint(equalTo: headerView.topAnchor, constant: 16).isActive = true
        leftIcon.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 16).isActive = true
        leftIcon.widthAnchor.constraint(equalToConstant: 24).isActive = true
        leftIcon.heightAnchor.constraint(equalToConstant: 24).isActive = true
        
        featureTitle.leadingAnchor.constraint(equalTo: leftIcon.trailingAnchor, constant: 16).isActive = true
        featureTitle.centerYAnchor.constraint(equalTo: leftIcon.centerYAnchor).isActive = true
        featureTitle.trailingAnchor.constraint(equalTo: arrowImage.leadingAnchor, constant: -16).isActive = true
        featureTitle.textColor = UIColor(red: 0.067, green: 0.067, blue: 0.067, alpha: 1)
        featureTitle.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)
        
        arrowImage.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -16).isActive = true
        arrowImage.centerYAnchor.constraint(equalTo: leftIcon.centerYAnchor).isActive = true
        arrowImage.widthAnchor.constraint(equalToConstant: 24).isActive = true
        arrowImage.heightAnchor.constraint(equalToConstant: 24).isActive = true
        arrowImage.image = UIImage(named: "arrowFaqDown")
        
        dottedLineImage.leadingAnchor.constraint(equalTo: leftIcon.leadingAnchor).isActive = true
        dottedLineImage.trailingAnchor.constraint(equalTo: arrowImage.trailingAnchor).isActive = true
        dottedLineImage.topAnchor.constraint(equalTo: leftIcon.bottomAnchor, constant: 16).isActive = true
        dottedLineImage.bottomAnchor.constraint(equalTo: headerView.bottomAnchor).isActive = true
        dottedLineImage.heightAnchor.constraint(equalToConstant: 1).isActive = true
        dottedLineImage.image = UIImage(named: "dottedLineImage")
        dottedLineImage.contentMode = .scaleToFill
        
        // Add single tap gesture to the entire header view for better user experience
        headerView.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapOnHeader))
        headerView.addGestureRecognizer(tapGesture)

        // Disable interaction on individual elements to prevent conflicts
        [featureTitle, leftIcon, arrowImage, dottedLineImage].forEach {
            $0.isUserInteractionEnabled = false
        }
        
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(collectionView)
        
        collectionView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16).isActive = true
        collectionView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 16).isActive = true
        collectionView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16).isActive = true
        collectionView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16).isActive = true
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(TagCollectionViewCell.self, forCellWithReuseIdentifier: "TagCell")
        collectionView.isScrollEnabled = false
        collectionView.showsVerticalScrollIndicator = false
    }
    
    public func configureCell(lstExtraOptionss: LstExtraOptionss?, indexPath: IndexPath) {
        self.indexPath = indexPath
        self.lstExtraOptionss = lstExtraOptionss
        self.extraOptions = lstExtraOptionss?.displayOptions
        self.values = []
        
        for item in self.extraOptions ?? [] {
            self.values.append(item.name ?? "")
        }
        
        featureTitle.text = self.lstExtraOptionss?.groupName ?? ""

        // Animate arrow rotation
        let isExpanded = self.lstExtraOptionss?.isExpanded ?? false
        let targetImage = UIImage(named: isExpanded ? "arrowFaqUp" : "arrowFaqDown")

        UIView.transition(with: arrowImage, duration: 0.3, options: .transitionCrossDissolve, animations: {
            self.arrowImage.image = targetImage
        }, completion: nil)

        collectionView.reloadData()

        // Force layout update to ensure proper height calculation
        DispatchQueue.main.async {
            self.collectionView.collectionViewLayout.invalidateLayout()
            self.collectionView.layoutIfNeeded()
            // Ensure the collection view recalculates its content size
            self.collectionView.setNeedsLayout()
        }

        // Animate collection view and dotted line visibility
        let isExpanded = self.lstExtraOptionss?.isExpanded ?? false
        collectionView.isHidden = !isExpanded
        dottedLineImage.isHidden = !isExpanded

        // Set initial alpha for smooth animation
        if isExpanded {
            collectionView.alpha = 0.0
            dottedLineImage.alpha = 0.0
            UIView.animate(withDuration: 0.3, delay: 0.1, options: .curveEaseInOut, animations: {
                self.collectionView.alpha = 1.0
                self.dottedLineImage.alpha = 1.0
            }, completion: nil)
        }
        
        leftIcon.loadImageFromUrl(imgUrl: self.lstExtraOptionss?.groupIcon ?? "")
    }
    
    @objc
    private func didTapOnHeader() {
        // Add visual feedback for tap with scale and alpha animation
        UIView.animate(withDuration: 0.1, animations: {
            self.headerView.transform = CGAffineTransform(scaleX: 0.98, y: 0.98)
            self.headerView.alpha = 0.8
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.headerView.transform = CGAffineTransform.identity
                self.headerView.alpha = 1.0
            }
        }

        delegate?.expandCollapse(indexPath: self.indexPath)
    }
}

extension SelfServiceEnhancedFeaturesTVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return values.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TagCell", for: indexPath) as! TagCollectionViewCell
        let option = extraOptions?[indexPath.item]
        cell.configure(with: values[indexPath.item], isSelected: option?.isSelected ?? false, itemId: option?.id, fromFeatures: true)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard var option = extraOptions?[indexPath.item] else { return }
        option.isSelected.toggle()
        extraOptions?[indexPath.item] = option
        delegate?.didSelect(item: option.id ?? 0, indexPath: self.indexPath ?? IndexPath(row: 0, section: 0), lstExtraOptionss: self.lstExtraOptionss)
        collectionView.reloadItems(at: [indexPath])
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let option = values[indexPath.item]
        let extraOption = extraOptions?[indexPath.item]

        // Use the same font as TagCollectionViewCell for consistency
        let font = UIFont(name: LanguageHelper.isEnglish ? (extraOption?.isSelected == true ? "Inter-SemiBold" : "Inter-Regular") : (extraOption?.isSelected == true ? "Cairo-SemiBold" : "Cairo-Regular"), size: LanguageHelper.isEnglish ? 12 : 10) ?? UIFont()

        // Use boundingRect for more accurate text size calculation
        let textSize = (option as NSString).boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        ).size

        // Add extra padding to prevent text clipping: horizontal padding (16 + 16) + extra margin for text rendering
        // Arabic text may need more space, so add more padding for Arabic
        let extraHorizontalPadding: CGFloat = LanguageHelper.isEnglish ? 8 : 12
        let totalHorizontalPadding: CGFloat = 32 + extraHorizontalPadding

        // Ensure minimum width and height
        let finalWidth = max(textSize.width + totalHorizontalPadding, 44)
        let finalHeight = max(textSize.height + 24, 44)

        return CGSize(width: ceil(finalWidth), height: ceil(finalHeight))
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
}
