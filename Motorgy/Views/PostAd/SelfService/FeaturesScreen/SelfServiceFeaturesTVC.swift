//
//  SelfServiceFeaturesTVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 26/03/2024.
//  Copyright © 2024 <PERSON><PERSON>. All rights reserved.
//

import UIKit

protocol SelfServiceEnhancedFeaturesTVCProtocol: AnyObject {
    func didSelect(item: Int, indexPath: IndexPath, lstExtraOptionss: LstExtraOptionss?)
    func expandCollapse(indexPath: IndexPath?)
}

class SelfServiceEnhancedFeaturesTVC: UITableViewCell {
    let containerView = UIView()
    let featureTitle = UILabel()
    let collectionView: UICollectionView
    let leftIcon = UIImageView()
    let arrowImage = UIImageView()
    let dottedLineImage = UIImageView()
    let headerView = UIView()
    
    var lstExtraOptionss: LstExtraOptionss?
    private var extraOptions: [ExtraOptions]?
    private var values = [String]()
    private var ids = [Int]()
    private var itemSelectedID: Int?
    weak var delegate: SelfServiceEnhancedFeaturesTVCProtocol?
    private var indexPath: IndexPath?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        let layout = LeftAlignedCollectionViewFlowLayout()
        // Remove estimatedItemSize to avoid conflicts with sizeForItemAt
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.scrollDirection = .vertical
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)

        super.init(style: style, reuseIdentifier: reuseIdentifier)

        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)
        
        containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24).isActive = true
        containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8).isActive = true
        containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24).isActive = true
        containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8).isActive = true
        containerView.layer.backgroundColor = UIColor.white.cgColor
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.hexStringToUIColor(hex: "#EAECF0").cgColor
        
        headerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(headerView)
        
        headerView.topAnchor.constraint(equalTo: containerView.topAnchor).isActive = true
        headerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor).isActive = true
        headerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor).isActive = true
        headerView.heightAnchor.constraint(equalToConstant: 56).isActive = true
        
        [featureTitle, leftIcon, arrowImage, dottedLineImage].forEach {
            $0.translatesAutoresizingMaskIntoConstraints = false
            headerView.addSubview($0)
        }
        
        leftIcon.topAnchor.constraint(equalTo: headerView.topAnchor, constant: 16).isActive = true
        leftIcon.leadingAnchor.constraint(equalTo: headerView.leadingAnchor, constant: 16).isActive = true
        leftIcon.widthAnchor.constraint(equalToConstant: 24).isActive = true
        leftIcon.heightAnchor.constraint(equalToConstant: 24).isActive = true
        
        featureTitle.leadingAnchor.constraint(equalTo: leftIcon.trailingAnchor, constant: 16).isActive = true
        featureTitle.centerYAnchor.constraint(equalTo: leftIcon.centerYAnchor).isActive = true
        featureTitle.trailingAnchor.constraint(equalTo: arrowImage.leadingAnchor, constant: -16).isActive = true
        featureTitle.textColor = UIColor(red: 0.067, green: 0.067, blue: 0.067, alpha: 1)
        featureTitle.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)
        
        arrowImage.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -16).isActive = true
        arrowImage.centerYAnchor.constraint(equalTo: leftIcon.centerYAnchor).isActive = true
        arrowImage.widthAnchor.constraint(equalToConstant: 24).isActive = true
        arrowImage.heightAnchor.constraint(equalToConstant: 24).isActive = true
        arrowImage.image = UIImage(named: "arrowFaqDown")
        
        dottedLineImage.leadingAnchor.constraint(equalTo: leftIcon.leadingAnchor).isActive = true
        dottedLineImage.trailingAnchor.constraint(equalTo: arrowImage.trailingAnchor).isActive = true
        dottedLineImage.topAnchor.constraint(equalTo: leftIcon.bottomAnchor, constant: 16).isActive = true
        dottedLineImage.bottomAnchor.constraint(equalTo: headerView.bottomAnchor).isActive = true
        dottedLineImage.heightAnchor.constraint(equalToConstant: 1).isActive = true
        dottedLineImage.image = UIImage(named: "dottedLineImage")
        dottedLineImage.contentMode = .scaleToFill
        
        [headerView, featureTitle, leftIcon, arrowImage, dottedLineImage].forEach {
            $0.isUserInteractionEnabled = true
            $0.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTapOnHeader)))
        }
        
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(collectionView)
        
        collectionView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16).isActive = true
        collectionView.topAnchor.constraint(equalTo: headerView.bottomAnchor, constant: 16).isActive = true
        collectionView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16).isActive = true
        collectionView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -16).isActive = true
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(TagCollectionViewCell.self, forCellWithReuseIdentifier: "TagCell")
        collectionView.isScrollEnabled = false
        collectionView.showsVerticalScrollIndicator = false
    }
    
    public func configureCell(lstExtraOptionss: LstExtraOptionss?, indexPath: IndexPath) {
        self.indexPath = indexPath
        self.lstExtraOptionss = lstExtraOptionss
        self.extraOptions = lstExtraOptionss?.displayOptions
        self.values = []
        
        for item in self.extraOptions ?? [] {
            self.values.append(item.name ?? "")
        }
        
        featureTitle.text = self.lstExtraOptionss?.groupName ?? ""
        
        arrowImage.image = UIImage(named: self.lstExtraOptionss?.isExpanded ?? false ? "arrowFaqUp" : "arrowFaqDown")
        
        collectionView.reloadData()

        // Force layout update to ensure proper height calculation
        DispatchQueue.main.async {
            self.collectionView.layoutIfNeeded()
            self.collectionView.collectionViewLayout.invalidateLayout()
        }

        collectionView.isHidden = self.lstExtraOptionss?.isExpanded == false
        dottedLineImage.isHidden = self.lstExtraOptionss?.isExpanded == false
        
        leftIcon.loadImageFromUrl(imgUrl: self.lstExtraOptionss?.groupIcon ?? "")
    }
    
    @objc
    private func didTapOnHeader() {
        delegate?.expandCollapse(indexPath: self.indexPath)
    }
}

extension SelfServiceEnhancedFeaturesTVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return values.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TagCell", for: indexPath) as! TagCollectionViewCell
        let option = extraOptions?[indexPath.item]
        cell.configure(with: values[indexPath.item], isSelected: option?.isSelected ?? false, itemId: option?.id, fromFeatures: true)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard var option = extraOptions?[indexPath.item] else { return }
        option.isSelected.toggle()
        extraOptions?[indexPath.item] = option
        delegate?.didSelect(item: option.id ?? 0, indexPath: self.indexPath ?? IndexPath(row: 0, section: 0), lstExtraOptionss: self.lstExtraOptionss)
        collectionView.reloadItems(at: [indexPath])
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let option = values[indexPath.item]
        let extraOption = extraOptions?[indexPath.item]

        // Use the same font as TagCollectionViewCell for consistency
        let font = UIFont(name: LanguageHelper.isEnglish ? (extraOption?.isSelected == true ? "Inter-SemiBold" : "Inter-Regular") : (extraOption?.isSelected == true ? "Cairo-SemiBold" : "Cairo-Regular"), size: LanguageHelper.isEnglish ? 12 : 10) ?? UIFont()

        let textSize = option.size(withAttributes: [.font: font])

        // Match TagCollectionViewCell constraints: horizontal padding (16 + 16), vertical padding (12 + 12)
        return CGSize(width: textSize.width + 32, height: textSize.height + 24)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
}
