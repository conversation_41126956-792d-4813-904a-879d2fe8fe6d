//
//  MyCarsSwiftUIViewModel.swift
//  Motorgy
//
//  Created by Augment Agent on 27/01/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI
import RxSwift
import Combine

@MainActor
class MyCarsSwiftUIViewModel: ObservableObject {
    @Published var cars: [Car] = []
    @Published var bundles: [BundleModelMyCars] = []
    @Published var filters: [DashboardCarStatusFilters] = []
    @Published var isLoading = false
    @Published var shouldShowFilters = false
    @Published var canLoadMore = false
    
    private let myCarsVM = MyCarsViewModel()
    private let disposeBag = DisposeBag()
    private var pageId = 1
    private var carsFilterId = 0
    private var isSelling = false
    private var isBuying = false
    
    func setupFlags(isBuying: Bool, isSelling: Bool) {
        self.isBuying = isBuying
        self.isSelling = isSelling
    }
    
    func loadCars() {
        guard !isLoading else { return }
        
        isLoading = true
        pageId = 1
        
        myCarsVM.getMyCarsData(pageId: pageId, carsFilterId: carsFilterId)
            .observe(on: MainScheduler.instance)
            .subscribe(onNext: { [weak self] _ in
                self?.handleCarsLoaded()
            }, onError: { [weak self] error in
                self?.isLoading = false
                print("Error loading cars: \(error)")
            })
            .disposed(by: disposeBag)
    }
    
    func loadMoreCars() {
        guard !isLoading && canLoadMore else { return }
        
        isLoading = true
        pageId += 1
        
        myCarsVM.getMyCarsData(pageId: pageId, carsFilterId: carsFilterId)
            .observe(on: MainScheduler.instance)
            .subscribe(onNext: { [weak self] _ in
                self?.handleCarsLoaded()
            }, onError: { [weak self] error in
                self?.isLoading = false
                print("Error loading more cars: \(error)")
            })
            .disposed(by: disposeBag)
    }
    
    func handleFilterSelection(_ filterId: Int?) {
        carsFilterId = filterId ?? 0
        pageId = 1
        loadCars()
    }
    
    private func handleCarsLoaded() {
        isLoading = false
        
        let newCars = myCarsVM.getMyCarsList()
        
        if pageId == 1 {
            cars = newCars
            
            // Set shouldShowFilters only on initial load
            if carsFilterId == 0 {
                shouldShowFilters = !newCars.isEmpty
            }
            
            // Update bundles and filters
            if let result = myCarsVM.getMyCarsResult() {
                bundles = result.bundles ?? []
                filters = result.dashboardCarStatusFilters ?? []
            }
        } else {
            // Append new cars for pagination
            cars.append(contentsOf: newCars)
        }
        
        // Check if we can load more
        if let result = myCarsVM.getMyCarsResult() {
            canLoadMore = (result.count ?? 0) > cars.count
        } else {
            canLoadMore = false
        }
    }
}

// MARK: - Car Extensions for SwiftUI
extension Car {
    var displayTitle: String {
        return titleWithoutYear ?? ""
    }
    
    var displayYear: String {
        return "\(year ?? 0)"
    }
    
    var displayMileage: String {
        return mileageName ?? ""
    }
    
    var displayStatus: String {
        return statusTitle ?? ""
    }
    
    var firstImageUrl: String {
        return lstImages?.first ?? ""
    }
    
    var progressImageUrl: String {
        return progressImage ?? ""
    }
    
    var hasLeads: Bool {
        return (buyerLeadsCount ?? 0) > 0
    }
    
    var leadsCount: Int {
        return buyerLeadsCount ?? 0
    }
    
    var isExpiredCar: Bool {
        return isExpired ?? false
    }
    
    var hasOfferStatus: Bool {
        return offerStatus == 11
    }
    
    var isSelfServiceCar: Bool {
        return adMainType == 5
    }
    
    var isParkedLotCar: Bool {
        return isParkedLot ?? false
    }
    
    var expirationInfo: (days: Int, hours: Int) {
        guard let expiredDateString = expiredDate,
              let expiryDate = getDateObjectManuallyWhenDateFormatIsSSSSSS(dateString: expiredDateString) else {
            return (0, 0)
        }
        
        let (days, hours) = daysAndHoursBetween(Date(), expiryDate)
        return (days, hours)
    }
    
    var shouldShowExpiredView: Bool {
        let (days, hours) = expirationInfo
        return isExpiredCar || (days == 0 && hours == 0) || isParkedLotCar
    }
    
    var shouldShowExpirationDays: Bool {
        let (days, hours) = expirationInfo
        return hasOfferStatus && !shouldShowExpiredView && (days > 0 || hours > 0)
    }
    
    var expirationText: String {
        let (days, hours) = expirationInfo
        
        if days > 0 {
            return "\(days) \(days == 1 ? "day" : "days")"
        } else if hours > 0 {
            return "\(hours) \(hours == 1 ? "hour" : "hours")"
        } else {
            return "Expired"
        }
    }
    
    var consumerPackageInfo: (backgroundColor: UIColor, text: String) {
        switch consumerPackageType {
        case 1:
            return (UIColor.hexStringToUIColor(hex: "#FFF3CD"), packageName ?? "")
        case 2:
            return (UIColor.hexStringToUIColor(hex: "#D1ECF1"), packageName ?? "")
        case 3:
            return (UIColor.hexStringToUIColor(hex: "#EAF5F8"), packageName ?? "")
        default:
            return (.clear, "")
        }
    }
    
    var boostPackageInfo: (isVisible: Bool, text: String) {
        if let _ = isPremium, 
           let boostName = boostPackageName, 
           !boostName.isEmpty && boostName != "null" {
            return (true, boostName)
        }
        return (false, "")
    }
}

// MARK: - Helper Functions
func getDateObjectManuallyWhenDateFormatIsSSSSSS(dateString: String) -> Date? {
    // Implementation for date parsing - you'll need to implement this based on your existing logic
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
    return formatter.date(from: dateString)
}

func daysAndHoursBetween(_ date1: Date, _ date2: Date) -> (Int, Int) {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.day, .hour], from: date1, to: date2)
    return (components.day ?? 0, components.hour ?? 0)
}
