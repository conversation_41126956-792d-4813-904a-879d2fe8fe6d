//
//  CarCardView.swift
//  Motorgy
//
//  Created by Augment Agent on 27/01/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct CarCardView: View {
    let car: Car
    let onLeadsTapped: () -> Void
    let onRepostTapped: () -> Void
    let onCarTapped: () -> Void
    
    var body: some View {
        Button(action: onCarTapped) {
            VStack(alignment: .leading, spacing: 0) {
                // Header section with car image and basic info
                headerSection
                
                // Dynamic content based on car status
                dynamicContentSection
            }
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack(spacing: 12) {
            // Car Image
            carImageView
            
            // Car Info
            VStack(alignment: .leading, spacing: 4) {
                Text(car.displayTitle)
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16))
                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                    .lineLimit(2)
                
                HStack(spacing: 8) {
                    Text(car.displayYear)
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                        .foregroundColor(Color(uiColor: Colors.slateColor))
                    
                    Text("•")
                        .foregroundColor(Color(uiColor: Colors.slateColor))
                    
                    Text(car.displayMileage)
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                        .foregroundColor(Color(uiColor: Colors.slateColor))
                }
                
                Spacer()
            }
            
            Spacer()
            
            // Status Image
            AsyncImage(url: URL(string: car.progressImageUrl)) { image in
                image
                    .resizable()
                    .scaledToFit()
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }
            .frame(width: 24, height: 24)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .frame(height: LanguageHelper.isEnglish ? 93 : 100)
    }
    
    // MARK: - Car Image View
    private var carImageView: some View {
        AsyncImage(url: URL(string: car.firstImageUrl)) { phase in
            if let image = phase.image {
                image
                    .resizable()
                    .scaledToFill()
            } else if phase.error != nil {
                Image("no-image")
                    .resizable()
                    .scaledToFill()
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
            }
        }
        .frame(width: 80, height: 50)
        .cornerRadius(8)
        .clipped()
    }
    
    // MARK: - Dynamic Content Section
    private var dynamicContentSection: some View {
        VStack(spacing: 12) {
            // Consumer Package Type Badge
            if car.consumerPackageType != nil && car.consumerPackageType != 0 {
                consumerPackageBadge
            }
            
            // Boost Package Badge
            if car.boostPackageInfo.isVisible {
                boostPackageBadge
            }
            
            // Expiration Info
            if car.shouldShowExpirationDays {
                expirationDaysView
            }
            
            // Buyer Leads
            if car.hasLeads {
                buyerLeadsView
            }
            
            // Status Section
            statusSection
            
            // Expired/Repost Section (for self-service cars)
            if car.isSelfServiceCar && car.shouldShowExpiredView {
                expiredRepostSection
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
    
    // MARK: - Consumer Package Badge
    private var consumerPackageBadge: some View {
        let packageInfo = car.consumerPackageInfo
        
        return HStack {
            Text(packageInfo.text)
                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12))
                .foregroundColor(.black)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(uiColor: packageInfo.backgroundColor))
                .cornerRadius(12)
            
            Spacer()
        }
    }
    
    // MARK: - Boost Package Badge
    private var boostPackageBadge: some View {
        HStack {
            Text(car.boostPackageInfo.text)
                .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12))
                .foregroundColor(.black)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(uiColor: UIColor.hexStringToUIColor(hex: "#FFF3CD")))
                .cornerRadius(12)
            
            Spacer()
        }
    }
    
    // MARK: - Expiration Days View
    private var expirationDaysView: some View {
        HStack {
            Text("Ad expires in ".localized)
                .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 12))
                .foregroundColor(Color(uiColor: Colors.slateColor))
            
            Text(car.expirationText)
                .font(.custom(LanguageHelper.isEnglish ? "Inter-Bold" : "Cairo-Bold", size: 12))
                .foregroundColor(Color(uiColor: Colors.slateColor))
            
            Spacer()
        }
        .frame(height: 18)
    }
    
    // MARK: - Buyer Leads View
    private var buyerLeadsView: some View {
        Button(action: onLeadsTapped) {
            HStack {
                Text("Buyers leads".localized)
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12))
                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                
                Spacer()
                
                HStack(spacing: 8) {
                    Text("\(car.leadsCount)")
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-Medium" : "Cairo-Medium", size: 10))
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color(uiColor: Colors.bluishColor))
                        .cornerRadius(8)
                    
                    Image("arrow-list-cars")
                        .resizable()
                        .frame(width: 18, height: 18)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(uiColor: UIColor.hexStringToUIColor(hex: "#F8F9FA")))
            .cornerRadius(8)
        }
        .frame(height: 38)
    }
    
    // MARK: - Status Section
    private var statusSection: some View {
        VStack(spacing: 8) {
            // Divider
            Rectangle()
                .fill(Color(uiColor: UIColor.hexStringToUIColor(hex: "#EAECF0")))
                .frame(height: 1)
            
            // Status Text
            HStack {
                Text(car.displayStatus)
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-Medium" : "Cairo-Medium", size: 14))
                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                
                Spacer()
            }
        }
        .frame(height: 49)
    }
    
    // MARK: - Expired/Repost Section
    private var expiredRepostSection: some View {
        VStack(spacing: 12) {
            // Divider
            Rectangle()
                .fill(Color(uiColor: UIColor.hexStringToUIColor(hex: "#EAECF0")))
                .frame(height: 1)
            
            HStack {
                Image("expiredIconnnn")
                    .resizable()
                    .frame(width: 18, height: 18)
                
                Text("Ad expired".localized)
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-Medium" : "Cairo-Medium", size: 14))
                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                
                Spacer()
                
                Button(action: onRepostTapped) {
                    Text("Repost".localized)
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 12))
                        .foregroundColor(Color(uiColor: Colors.bluishColor))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color(uiColor: Colors.bluishColor), lineWidth: 1)
                        )
                }
            }
        }
        .frame(height: 55)
    }
}
