//
//  MyCarsSwiftUIView.swift
//  Motorgy
//
//  Created by Augment Agent on 27/01/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct MyCarsSwiftUIView: View {
    @StateObject private var viewModel = MyCarsSwiftUIViewModel()
    @State private var scrollOffset: CGFloat = 0
    @State private var isHeaderVisible = true
    
    let isSelling: Bool
    let isBuying: Bool
    
    // Callbacks for navigation
    let onBuyBundleTapped: () -> Void
    let onListCarTapped: (BundleModelMyCars) -> Void
    let onInfoIconTapped: (BundleModelMyCars) -> Void
    let onRenewTapped: () -> Void
    let onOpenLeadsScreen: (Car) -> Void
    let onRepostCar: (Int) -> Void
    let onCarTapped: (Car) -> Void
    let onBrowseCars: () -> Void
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .top) {
                // Background
                Color.white
                    .ignoresSafeArea()
                
                if viewModel.isLoading && viewModel.cars.isEmpty {
                    // Loading state
                    ProgressView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.cars.isEmpty {
                    // Empty state
                    EmptyStateView(
                        isBuying: isBuying,
                        onBrowseCars: onBrowseCars
                    )
                } else {
                    // Main content with cars
                    VStack(spacing: 0) {
                        // Sticky header area
                        stickyHeaderArea
                        
                        // Cars collection
                        carsCollectionView
                    }
                }
            }
        }
        .onAppear {
            viewModel.setupFlags(isBuying: isBuying, isSelling: isSelling)
            viewModel.loadCars()
        }
    }
    
    // MARK: - Sticky Header Area
    private var stickyHeaderArea: some View {
        VStack(spacing: 0) {
            // Micro Dealer Header (shows/hides based on scroll)
            if isSelling && !viewModel.bundles.isEmpty {
                MicroDealerHeaderView(
                    bundles: viewModel.bundles,
                    onBuyBundleTapped: onBuyBundleTapped,
                    onListCarTapped: onListCarTapped,
                    onInfoIconTapped: onInfoIconTapped,
                    onRenewTapped: onRenewTapped
                )
                .opacity(isHeaderVisible ? 1 : 0)
                .animation(.easeInOut(duration: 0.6), value: isHeaderVisible)
            }
            
            // Car Filters (always sticky when selling)
            if isSelling && viewModel.shouldShowFilters {
                CarFiltersView(
                    filters: viewModel.filters,
                    onFilterTapped: { filterId in
                        viewModel.handleFilterSelection(filterId)
                    }
                )
                .background(Color.white)
                .shadow(color: isHeaderVisible ? .clear : .black.opacity(0.1), radius: 4, x: 0, y: 2)
                .animation(.easeInOut(duration: 0.6), value: isHeaderVisible)
            }
            
            // List Car Button (for selling)
            if isSelling {
                listCarButton
            }
        }
        .background(Color.white)
    }
    
    // MARK: - Cars Collection View
    private var carsCollectionView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 15) {
                    ForEach(viewModel.cars, id: \.iD) { car in
                        CarCardView(
                            car: car,
                            onLeadsTapped: {
                                onOpenLeadsScreen(car)
                            },
                            onRepostTapped: {
                                onRepostCar(car.iD ?? 0)
                            },
                            onCarTapped: {
                                onCarTapped(car)
                            }
                        )
                        .padding(.horizontal, 16)
                    }
                    
                    // Load more indicator
                    if viewModel.canLoadMore {
                        ProgressView()
                            .padding()
                            .onAppear {
                                viewModel.loadMoreCars()
                            }
                    }
                }
                .padding(.top, 16)
                .background(
                    GeometryReader { geometry in
                        Color.clear
                            .preference(key: ScrollOffsetPreferenceKey.self, value: geometry.frame(in: .named("scroll")).minY)
                    }
                )
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                handleScrollOffset(value)
            }
        }
    }
    
    // MARK: - List Car Button
    private var listCarButton: some View {
        Button(action: {
            // Handle list car action
        }) {
            HStack {
                Text(LanguageHelper.isEnglish ? "List new car" : "آضف سيارة جديدة")
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16))
                    .foregroundColor(Color(uiColor: UIColor.hexStringToUIColor(hex: "#0078FF")))
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color(uiColor: UIColor.hexStringToUIColor(hex: "#E6F2FF")))
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .shadow(color: Color(uiColor: Colors.topShadowBorderColor), radius: 10, x: 0, y: 2)
    }
    
    // MARK: - Scroll Handling
    private func handleScrollOffset(_ offset: CGFloat) {
        let threshold: CGFloat = 120
        
        withAnimation(.easeInOut(duration: 0.6)) {
            if offset < -threshold {
                // Scrolling down - hide header
                isHeaderVisible = false
            } else if offset > -threshold {
                // Scrolling up - show header
                isHeaderVisible = true
            }
        }
    }
}

// MARK: - Scroll Offset Preference Key
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Empty State View
struct EmptyStateView: View {
    let isBuying: Bool
    let onBrowseCars: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Empty state content
            VStack(spacing: 16) {
                Text(isBuying ? "You're not buying any cars yet.".localized : "No cars found")
                    .font(.custom(LanguageHelper.isEnglish ? "Inter-Medium" : "Cairo-Medium", size: 18))
                    .foregroundColor(Color(uiColor: Colors.charcoalColor))
                    .multilineTextAlignment(.center)
                
                if isBuying {
                    Text("Buy your next car from a wide selection of pre-inspected cars 100% guaranteed by experts.".localized)
                        .font(.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14))
                        .foregroundColor(Color(uiColor: Colors.slateColor))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 32)
                    
                    Button(action: onBrowseCars) {
                        Text("Browse Cars".localized)
                            .font(.custom(LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16))
                            .foregroundColor(.white)
                            .padding(.horizontal, 32)
                            .padding(.vertical, 12)
                            .background(Color(uiColor: Colors.bluishColor))
                            .cornerRadius(8)
                    }
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 24)
    }
}
