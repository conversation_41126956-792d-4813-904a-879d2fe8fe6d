//
//  MyCarsSwiftUIHostingController.swift
//  Motorgy
//
//  Created by Augment Agent on 27/01/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import UIKit
import SwiftUI

class MyCarsSwiftUIHostingController: UIHostingController<MyCarsSwiftUIView> {
    
    // MARK: - Properties
    private var isSelling: Bool = false
    private var isBuying: Bool = false
    private var microDealerViewModel: MicroDealerViewModel?
    
    // MARK: - Initialization
    init(isSelling: Bool, isBuying: Bool) {
        self.isSelling = isSelling
        self.isBuying = isBuying
        
        let swiftUIView = MyCarsSwiftUIView(
            isSelling: isSelling,
            isBuying: isBuying,
            onBuyBundleTapped: { },
            onListCarTapped: { _ in },
            onInfoIconTapped: { _ in },
            onRenewTapped: { },
            onOpenLeadsScreen: { _ in },
            onRepostCar: { _ in },
            onCarTapped: { _ in },
            onBrowseCars: { }
        )
        
        super.init(rootView: swiftUIView)
        
        setupCallbacks()
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - View Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupNavigationBar()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // Hide navigation bar for clean SwiftUI experience
        navigationController?.setNavigationBarHidden(false, animated: animated)
        
        // Set title based on mode
        if isSelling {
            title = "Cars I Sell".localized
        } else if isBuying {
            title = "Cars I Buy".localized
        } else {
            title = "My Cars".localized
        }
    }
    
    // MARK: - Setup Methods
    private func setupNavigationBar() {
        // Configure navigation bar appearance
        navigationController?.navigationBar.prefersLargeTitles = false
        navigationController?.navigationBar.tintColor = Colors.bluishColor
        
        // Add back button if needed
        if navigationController?.viewControllers.count ?? 0 > 1 {
            navigationItem.leftBarButtonItem = UIBarButtonItem(
                image: UIImage(systemName: "chevron.left"),
                style: .plain,
                target: self,
                action: #selector(backButtonTapped)
            )
        }
    }
    
    private func setupCallbacks() {
        let updatedView = MyCarsSwiftUIView(
            isSelling: isSelling,
            isBuying: isBuying,
            onBuyBundleTapped: { [weak self] in
                self?.navigateToBuyBundle()
            },
            onListCarTapped: { [weak self] bundle in
                self?.microDealerListCarFlow(from: bundle)
            },
            onInfoIconTapped: { [weak self] bundle in
                self?.showInfoPopup(bundle: bundle)
            },
            onRenewTapped: { [weak self] in
                self?.navigateToRenewBundle()
            },
            onOpenLeadsScreen: { [weak self] car in
                self?.openBuyerLeadsScreen(car: car)
            },
            onRepostCar: { [weak self] carId in
                self?.repostExpiredCar(carId: carId)
            },
            onCarTapped: { [weak self] car in
                self?.navigateToCarDetails(car: car)
            },
            onBrowseCars: { [weak self] in
                self?.navigateToBrowseCars()
            }
        )
        
        rootView = updatedView
    }
    
    // MARK: - Navigation Actions
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    private func navigateToBuyBundle() {
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
        guard let navigationController = self.navigationController else { return }
        SellingMultipleRouter.build(from: navigationController)
    }
    
    private func microDealerListCarFlow(from bundle: BundleModelMyCars) {
        // Implementation for listing car flow
        checkForActiveBundlesBeforeListingCar()
    }
    
    private func showInfoPopup(bundle: BundleModelMyCars) {
        DispatchQueue.main.async { [weak self] in
            let popupView = BundleInfoPopupView(bundle: bundle) { [weak self] in
                self?.dismiss(animated: true)
            }
            let hostingController = UIHostingController(rootView: popupView)
            hostingController.modalPresentationStyle = .overFullScreen
            hostingController.modalTransitionStyle = .crossDissolve
            hostingController.view.backgroundColor = .clear
            self?.present(hostingController, animated: true)
        }
    }
    
    private func navigateToRenewBundle() {
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = true
        guard let navigationController = self.navigationController else { return }
        SellingMultipleRouter.build(from: navigationController)
    }
    
    private func openBuyerLeadsScreen(car: Car) {
        // Navigate to buyer leads screen
        let buyerLeadsVC = getBuyerLeadsViewController()
        buyerLeadsVC.setCarId(carId: car.iD)
        navigationController?.pushViewController(buyerLeadsVC, animated: true)
    }
    
    private func repostExpiredCar(carId: Int) {
        // Handle repost car logic
        let alertController = UIAlertController(
            title: "Repost Car".localized,
            message: "Are you sure you want to repost this car?".localized,
            preferredStyle: .alert
        )
        
        alertController.addAction(UIAlertAction(title: "Cancel".localized, style: .cancel))
        alertController.addAction(UIAlertAction(title: "Repost".localized, style: .default) { [weak self] _ in
            self?.performRepostCar(carId: carId)
        })
        
        present(alertController, animated: true)
    }
    
    private func navigateToCarDetails(car: Car) {
        let myCarStatusVC = getMyCarStatusViewController()
        let isSelfServiceCar = car.adMainType == 5
        myCarStatusVC.setCarId(carId: car.iD, isSelfServiceCar: isSelfServiceCar)
        myCarStatusVC.isCarMicroDealer(isMicroDealer: car.consumerPackageType == 3)
        navigationController?.pushViewController(myCarStatusVC, animated: true)
    }
    
    private func navigateToBrowseCars() {
        // Navigate to browse cars screen
        navigationController?.popToRootViewController(animated: true)
    }
    
    // MARK: - Helper Methods
    private func checkForActiveBundlesBeforeListingCar() {
        if UserHelper.user.isLogin() {
            let mobileNumber = SharedHelper.shared.getFromDefault(key: "mobile")
            
            if mobileNumber.isEmpty || mobileNumber == "" || mobileNumber == "00000000" {
                checkForMobileNumberFirst()
            } else {
                microDealerViewModel = MicroDealerViewModel(navigationController: self.navigationController)
                microDealerViewModel?.checkForActiveBundlesBeforeListingCar()
            }
        }
    }
    
    private func checkForMobileNumberFirst() {
        let validationVC = getMobileNumberValidationViewController()
        validationVC.modalPresentationStyle = .custom
        present(validationVC, animated: true)
    }
    
    private func performRepostCar(carId: Int) {
        // Implement repost car API call
        // This would typically involve calling the appropriate service
        print("Reposting car with ID: \(carId)")
    }
    
    // MARK: - Factory Methods
    private func getBuyerLeadsViewController() -> BuyerLeadsVC {
        return getNextViewController(
            viewControllerClass: BuyerLeadsVC.self,
            storyBoardName: "Account",
            identifier: "BuyerLeadsVC"
        ) ?? BuyerLeadsVC()
    }
    
    private func getMyCarStatusViewController() -> MyCarStatusVC {
        return getNextViewController(
            viewControllerClass: MyCarStatusVC.self,
            storyBoardName: "Account",
            identifier: "MyCarStatusVC"
        ) ?? MyCarStatusVC()
    }
    
    private func getMobileNumberValidationViewController() -> MobileNumberValidationVC {
        return getNextViewController(
            viewControllerClass: MobileNumberValidationVC.self,
            storyBoardName: "Authentication",
            identifier: "MobileNumberValidationVC"
        ) ?? MobileNumberValidationVC()
    }
}

// MARK: - BaseVC Extension
extension MyCarsSwiftUIHostingController {
    func getNextViewController<T: UIViewController>(
        viewControllerClass: T.Type,
        storyBoardName: String,
        identifier: String
    ) -> T? {
        let storyboard = UIStoryboard(name: storyBoardName, bundle: nil)
        return storyboard.instantiateViewController(withIdentifier: identifier) as? T
    }
}

// MARK: - Public Interface
extension MyCarsSwiftUIHostingController {
    func setupFlagsFromMyGarageScreen(isBuying: Bool, isSelling: Bool) {
        self.isBuying = isBuying
        self.isSelling = isSelling
        setupCallbacks()
    }
}
