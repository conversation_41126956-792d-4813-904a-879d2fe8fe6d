//
//  MyCarsCVHandler.swift
//  Motorgy
//
//  Created by ahmed ezz on 4/12/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit

class MyCarsCVHandler: NSObject  ,UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {
    
    // MARK: - Values
    private weak var cv: UICollectionView!
    private weak var controller: BaseVC?
    private var myCarsList: [Car] = []
    private weak var onLoadMore: OnLoadMore?
    public var openLeadsScreenCallBackFromMyCars: ((Car?) -> Void)?
    public var repostCarButtonTapped: ((Int) -> Void)?
    public var onScrollCallback: ((UIScrollView) -> Void)?
    
    // MARK: - init
    init(cv: UICollectionView, controller: BaseVC?, onLoadMore: OnLoadMore?) {
        super.init()
        self.cv = cv
        self.controller = controller
        self.cv.register(UINib(nibName: "MyCarNewCVC", bundle: nil), forCellWithReuseIdentifier: "MyCarNewCVC")
        self.cv.delegate = self
        self.cv.dataSource = self
        self.onLoadMore = onLoadMore
    }
    
    // MARK: - showData
    public func showData(myCarsList: [Car]) {
        self.myCarsList = myCarsList
        self.cv.reloadData()
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return myCarsList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MyCarNewCVC", for: indexPath) as! MyCarNewCVC
        cell.configureCell(car: myCarsList[indexPath.row])
        cell.openLeadsScreenCallBackFromMyCars = { [weak self] car in
            self?.openLeadsScreenCallBackFromMyCars?(car)
        }
        cell.repostCarButtonTapped = { [weak self] carId in
            self?.repostCarButtonTapped?(carId)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 15
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 15
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let car = self.myCarsList[indexPath.row]
        
        let hasBuyerLeads = car.buyerLeadsCount ?? 0 > 0
        let isExpired = car.isExpired ?? false
        let offerStatus = car.offerStatus == 11
        
        let headerViewHeight = LanguageHelper.isEnglish ? 93 : 100
        let expiresInDaysViewHeight = 18
        let leadsCountViewHeight = 38
        let statusViewHeight = 49
        let expiredViewHeight = 55
        let stackViewSpacing = 12
        let stackViewTopMargin = 0
        
        var totalHeight = headerViewHeight + stackViewTopMargin
        
        var expiryDaysCount = 0
        var expiryHoursCount = 0
        
        if let expiryDate = getDateObjectManuallyWhenDateFormatIsSSSSSS(dateString: car.expiredDate ?? "") {
            let (days, hours) = daysAndHoursBetween(Date(), expiryDate)
            expiryDaysCount = days
            expiryHoursCount = hours
        } else {
            expiryDaysCount = 0
            expiryHoursCount = 0
        }
        
        if offerStatus {
            if isExpired || (expiryDaysCount == 0 && expiryHoursCount == 0) || car.isParkedLot ?? false  {
                totalHeight += expiredViewHeight + stackViewSpacing
            } else {
                totalHeight += expiresInDaysViewHeight + stackViewSpacing
                if hasBuyerLeads {
                    totalHeight += leadsCountViewHeight + stackViewSpacing
                }
                totalHeight += statusViewHeight + stackViewSpacing
            }
        } else {
            totalHeight += statusViewHeight + stackViewSpacing
        }
        
        return CGSize(width: collectionView.bounds.width, height: CGFloat(totalHeight + 15 + 16))
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let car = myCarsList[indexPath.row]
        self.moveToCarDetails(car: car)
    }
    
    private func moveToCarDetails(car: Car?) {
//        let myCarDetailsVC = self.controller?.getNextViewController(viewControllerClass: MyCarDetailsVC.self,storyBoardName: "Account", identifier: "MyCarDetailsVC") ?? MyCarDetailsVC()
//        myCarDetailsVC.setCarId(carId: car?.iD)
//        self.controller?.navigationController?.pushViewController(myCarDetailsVC, animated: true)
        
        let myCarStatusVC = self.controller?.getNextViewController(viewControllerClass: MyCarStatusVC.self,storyBoardName: "Account", identifier: "MyCarStatusVC") ?? MyCarStatusVC()
        let isSelfServiceCar = car?.adMainType == 5
        myCarStatusVC.setCarId(carId: car?.iD, isSelfServiceCar: isSelfServiceCar)
        myCarStatusVC.isCarMicroDealer(isMicroDealer: car?.consumerPackageType == 3)
        self.controller?.navigationController?.pushViewController(myCarStatusVC, animated: true)

    }
    
    // MARK: - Scroll View Delegate Methods
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // Forward scroll events to the callback for sticky header functionality
        onScrollCallback?(scrollView)
    }

    // load More Functions
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            scrolledToBottom(scrollView)
        }
    }
    
    // check if scroll view arrive to bottom
    private func scrolledToBottom(_ scrollview: UIScrollView) {
        let bottomEdge = scrollview.contentOffset.y + scrollview.frame.size.height
        if bottomEdge >= scrollview.contentSize.height {
            print("load More")
            onLoadMore?.loadMore!()
        }
    }
    
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        scrolledToBottom(scrollView)
    }
    
}
