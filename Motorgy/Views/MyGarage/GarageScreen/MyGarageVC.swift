//
//  MyGarageVC.swift
//  Motorgy
//
//  Created by <PERSON><PERSON><PERSON>b Ashraf on 27/10/2024.
//  Copyright © 2024 <PERSON><PERSON>z. All rights reserved.
//

import UIKit
import RxSwift
import FirebaseAnalytics
import FBSDKCoreKit

class MyGarageVC: BaseVC {
    @IBOutlet weak private var tableView: UITableView!
    @IBOutlet weak var notLoggedInView: UIView!
    @IBOutlet weak var loggedInDescLabel: labelLocalization!
    @IBOutlet weak var loginButton: buttonLocalization!
    
    private var carDetailsViewModel = MyCarDetailsViewModel()
    private var result: Result?
    private var openSellerDashboardFromPushNotification = false
    private var openSellerDashboardFromPushNotificationId = 0
    private var openOrderDetailsScreenFromPushNotification = false
    private var serviceRequestId = 0
    
    private enum MyGarageSections: Int, CaseIterable {
        case chats
        case buyAndCell
        case carServices
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.tabBarController?.delegate = self
        self.navigationItem.backButtonTitle = " "
    }
    
    func setOpenOrderDetailsScreenFromPushNotification(openOrderDetailsScreenFromPushNotification: Bool, serviceRequestId: Int) {
        self.openOrderDetailsScreenFromPushNotification = openOrderDetailsScreenFromPushNotification
        self.serviceRequestId = serviceRequestId
    }
    
    override func viewWillAppear(_ animated: Bool) {
        self.tabBarController?.navigationItem.leftBarButtonItems = []
        self.parent?.navigationItem.titleView = nil
        
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(true, animated: true)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = true
        } else {
            self.navigationController?.setNavigationBarHidden(true, animated: true)
        }
        
        loggedInDescLabel.text = "Login or Sign up to control your car sales, purchases, chats, and services in one place.".localized
        loggedInDescLabel.textAlignment = .center
        loggedInDescLabel.numberOfLines = 0
        loggedInDescLabel.textColor = Colors.slateColor
        loggedInDescLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: 14)
        
        loginButton.cornerRadius = 8
        loginButton.setTitle("Login or Sign up".localized, for: .normal)
        loginButton.setTitleColor(UIColor.hexStringToUIColor(hex: "#0078FF"), for: .normal)
        loginButton.backgroundColor = UIColor.hexStringToUIColor(hex: "#E6F2FF")
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        
        var buttonConfig = UIButton.Configuration.plain()
        buttonConfig.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer({ incoming in
            var outgoing = incoming
            outgoing.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 16)
            return outgoing
        })
        
        loginButton.configuration = buttonConfig
        
        self.tableView.delegate = self
        self.tableView.dataSource = self
        self.tableView.showsVerticalScrollIndicator = false
        self.tableView.register(MyGarageChatsTVC.nib(), forCellReuseIdentifier: MyGarageChatsTVC.identifier)
        self.tableView.register(MyGarageBuyAndCellTVC.nib(), forCellReuseIdentifier: MyGarageBuyAndCellTVC.identifier)
        self.tableView.register(MyGarageCarServicesTVC.nib(), forCellReuseIdentifier: MyGarageCarServicesTVC.identifier)
        self.tableView.sectionHeaderTopPadding = 0
        self.tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        self.tableView.contentInsetAdjustmentBehavior = .never
        
        if UserHelper.user.isLogin() {
            self.notLoggedInView.isHidden = true
            self.tableView.isHidden = true
            
            self.carDetailsViewModel.marketPlaceMarkGetMyDashboardCount().bind { [weak self] result in
                if result?.aPIStatus ?? 0 == 1 {
                    DispatchQueue.main.async { [weak self] in
                        self?.result = result
                        self?.tableView.isHidden = false
                        self?.tableView.reloadData()
                        
                        if result?.totalNotifications ?? 0 > 0 {
                            self?.tabBarItem.badgeValue = result?.totalNotifications?.description
                        } else {
                            self?.tabBarItem.badgeValue = nil
                        }
                    }
                }
            }.disposed(by: carDetailsViewModel.getDisposeBag())
        } else {
            self.notLoggedInView.isHidden = false
            self.tableView.isHidden = true
        }
        
        if self.openSellerDashboardFromPushNotification {
            self.openSellerDashboardFromPushNotification = false
            
            DispatchQueue.main.async {
                
                if self.openSellerDashboardFromPushNotificationId != 0 {
                    self.navigateToSellerDashboard()
                }
            }
        }
        
        if self.openOrderDetailsScreenFromPushNotification && self.serviceRequestId != 0 {
            DispatchQueue.main.async {
                let orderDetailsVC = self.getNextViewController(viewControllerClass: MyOrderDetailsVC.self, storyBoardName: "MarketPlace", identifier: "MyOrderDetailsVC") ?? MyOrderDetailsVC()
                orderDetailsVC.setServiceRequestId(serviceRequestId: self.serviceRequestId)
                orderDetailsVC.setNavigationBar(setNavigationBar: true)
                let navigationController = UINavigationController(rootViewController: orderDetailsVC)
                navigationController.modalPresentationStyle = .fullScreen
                self.present(navigationController, animated: false)
                self.openOrderDetailsScreenFromPushNotification = false
                self.serviceRequestId = 0
            }
        }
    }
    
    public func setOpenSellerDashboardFromPushNotification(adId: Int) {
        self.openSellerDashboardFromPushNotification = true
        self.openSellerDashboardFromPushNotificationId = adId
        
        if self.isViewLoaded && self.view.window != nil {
            DispatchQueue.main.async { [weak self] in
                self?.navigateToSellerDashboard()
            }
        }
    }
    
    private func navigateToSellerDashboard() {
        guard ConstantsValues.sharedInstance.adIdSelfServiceFromPushNotification != 0 else { return }
        let myCarDetailsVC = UIStoryboard.init(name: "Account", bundle: nil).instantiateViewController(withIdentifier: "MyCarStatusVC") as! MyCarStatusVC
        myCarDetailsVC.setCarId(
            carId: ConstantsValues.sharedInstance.adIdSelfServiceFromPushNotification,
            isSelfServiceCar: ConstantsValues.sharedInstance.isSelfServiceFlagFromPushNotification
        )
        myCarDetailsVC.setOpenFromHomeScreen(isOpenFromHomeScreen: true)
        let navigationController = UINavigationController(rootViewController: myCarDetailsVC)
        navigationController.modalPresentationStyle = .fullScreen
        self.present(navigationController, animated: true, completion: nil)
        
        self.openSellerDashboardFromPushNotification = false
        self.openSellerDashboardFromPushNotificationId = 0
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        self.parent?.navigationItem.rightBarButtonItem = nil
    }
    
    @available(iOS 12.0, *)
    private func getChatsListScreen() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let chatList = self.getNextViewController(viewControllerClass: ChatListVC.self, storyBoardName: "Chat", identifier: "ChatListVC") ?? ChatListVC()
            if #available(iOS 18.0, *) {
                self.navigationController?.setNavigationBarHidden(false, animated: false)
            } else if #available(iOS 17.0, *) {
                self.navigationController?.navigationBar.isHidden = false
            } else {
                self.navigationController?.setNavigationBarHidden(false, animated: false)
            }
            self.navigationController?.pushViewController(chatList, animated: true)
        }
    }
    
    @objc
    private func loginButtonTapped() {
        self.moveToLogin()
    }
}

extension MyGarageVC: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        return MyGarageSections.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return result?.aPIStatus ?? 0 == 1 ? 1 : 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
            case MyGarageSections.chats.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MyGarageChatsTVC.identifier, for: indexPath) as? MyGarageChatsTVC
                cell?.selectionStyle = .none
                cell?.configureCell(controller: self, result: self.result)
                return cell ?? UITableViewCell()
                
            case MyGarageVC.MyGarageSections.buyAndCell.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MyGarageBuyAndCellTVC.identifier, for: indexPath) as? MyGarageBuyAndCellTVC
                cell?.selectionStyle = .none
                cell?.configureCell(controller: self, result: self.result)
                return cell ?? UITableViewCell()
                
            case MyGarageVC.MyGarageSections.carServices.rawValue:
                let cell = tableView.dequeueReusableCell(withIdentifier: MyGarageCarServicesTVC.identifier, for: indexPath) as? MyGarageCarServicesTVC
                cell?.selectionStyle = .none
                cell?.configureCell(controller: self, result: self.result)
                return cell ?? UITableViewCell()
                
            default:
                return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
}

extension MyGarageVC: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        if viewController is AccountVC || viewController is HomeVC || viewController is MyGarageVC {
            if #available(iOS 18.0, *) {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            } else if #available(iOS 17.0, *) {
                self.navigationController?.navigationBar.isHidden = true
            } else {
                self.navigationController?.setNavigationBarHidden(true, animated: true)
            }
        }
    }
}

extension MyGarageVC: MyGarageChatsTVCDelegate {
    func didTapChats() {
#if DEVELOPMENT
#else
        Analytics.logEvent("chat_list", parameters: [:])
        //        AppEvents.shared.logEvent(AppEvents.Name.init(rawValue: "chat_list"), parameters: [:])
#endif
        
        if #available(iOS 12.0, *) {
            AppHelper.shared.checkNotificationPermission { status in
                if status != 2 {
                    let sessionBegin = SharedHelper.shared.getIntValueFromDefault(key: "showFullNotificationScreenPerVisit")
                    
                    DispatchQueue.main.async {
                        if sessionBegin == 0 {
                            let notificationPermissionVC = self.getNextViewController(viewControllerClass: NotificationPermissionVC.self, storyBoardName: "Account", identifier: "NotificationPermissionVC") ?? NotificationPermissionVC()
                            notificationPermissionVC.modalPresentationStyle = .fullScreen
                            self.present(notificationPermissionVC, animated: false) {
                                self.getChatsListScreen()
                            }
                            
                            SharedHelper.shared.saveIntValueInDefault(key: "showFullNotificationScreenPerVisit", value: 1)
                        } else {
                            self.getChatsListScreen()
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        self.getChatsListScreen()
                    }
                }
            }
        }
    }
    
    func didTapNotifications() {
        let notificationsVC = self.getNextViewController(viewControllerClass: NotificationsVC.self,storyBoardName:"Main", identifier: "NotificationsVC") ?? NotificationsVC()
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(notificationsVC, animated: true)
    }
}

extension MyGarageVC: MyGarageBuyAndCellTVCDelegate {
    func didTapCarsIBuy() {
        // Use SwiftUI version for better sticky header functionality
        let myCarsSwiftUIVC = MyCarsSwiftUIHostingController(isSelling: false, isBuying: true)

        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(myCarsSwiftUIVC, animated: true)
    }
    
    func didTapCarsISell() {
        // Use SwiftUI version for better sticky header functionality
        let myCarsSwiftUIVC = MyCarsSwiftUIHostingController(isSelling: true, isBuying: false)

        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(myCarsSwiftUIVC, animated: true)
    }
    
    func didTapSavedSearch() {
#if DEVELOPMENT
#else
        Analytics.logEvent("save_search_screen", parameters: [:])
#endif
        let savedSearchVC = self.getNextViewController(viewControllerClass: SavedSearchVC.self,storyBoardName:"Account", identifier: "SavedSearchVC") ?? SavedSearchVC()
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(savedSearchVC, animated: true)
    }
    
    func didTapFavoriteCars() {
#if DEVELOPMENT
#else
        Analytics.logEvent("favourite_screen", parameters: ["trigger":"side_menu_screen"])
#endif
        
        let favouriteVC = self.getNextViewController(viewControllerClass: FavouriteVC.self,storyBoardName:"Account", identifier: "FavouriteVC") ?? FavouriteVC()
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(favouriteVC, animated: true)
    }
}
extension MyGarageVC: MyGarageCarServicesTVCDelegate {
    func didTapOrders() {
        let ordersVCs = self.getNextViewController(viewControllerClass: MyOrdersListVC.self,storyBoardName:"MarketPlace", identifier: "MyOrdersListVC") ?? MyOrdersListVC()
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(ordersVCs, animated: true)
    }
    
    func didTapVendors() {
        let favouriteVendorsVC = self.getNextViewController(viewControllerClass: FavouriteVendorsVC.self,storyBoardName:"MarketPlace", identifier: "FavouriteVendorsVC") ?? FavouriteVendorsVC()
        if #available(iOS 18.0, *) {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        } else if #available(iOS 17.0, *) {
            self.navigationController?.navigationBar.isHidden = false
        } else {
            self.navigationController?.setNavigationBarHidden(false, animated: false)
        }
        self.navigationController?.pushViewController(favouriteVendorsVC, animated: true)
    }
}
